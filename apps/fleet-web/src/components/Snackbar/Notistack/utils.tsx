import { useMemo, useState } from 'react'
import { Box, Button, IconButton, Tooltip, type ButtonProps } from '@karoo-ui/core'
import CloseIcon from '@mui/icons-material/Close'
import CopyIcon from '@mui/icons-material/CopyAll'
import FileCopyIcon from '@mui/icons-material/FileCopy'
import {
  closeSnackbar,
  enqueueSnackbar,
  type OptionsObject,
  type SnackbarMessage,
} from 'notistack'
import CopyToClipboard from 'react-copy-to-clipboard'
import type { Except, RequireAtLeastOne } from 'type-fest'

import { ctIntl } from 'src/util-components/ctIntl'

// Component to handle copy functionality with its own state
const CopyableSnackbarContent = ({ message }: { message: SnackbarMessage }) => {
  const [isCopied, setIsCopied] = useState(false)

  return (
    <Box
      display="flex"
      alignItems="center"
      gap={1}
    >
      {message}
      <CopyToClipboard
        text={message as string}
        onCopy={() => setIsCopied(true)}
      >
        <Tooltip
          title={ctIntl.formatMessage({
            id: isCopied ? 'Copied Successfully' : 'Copy to Clipboard',
          })}
          placement="top"
        >
          <IconButton
            size="small"
            data-testid={`CopyButton-${message}`}
          >
            {isCopied ? (
              <FileCopyIcon
                fontSize="small"
                sx={{ color: 'white' }}
              />
            ) : (
              <CopyIcon
                fontSize="small"
                sx={{ color: 'white' }}
              />
            )}
          </IconButton>
        </Tooltip>
      </CopyToClipboard>
    </Box>
  )
}

export const enqueueSnackbarWithCloseAction = (
  message: SnackbarMessage,
  {
    showEndIcon = true,
    ...options
  }: RequireAtLeastOne<OptionsObject, 'variant'> & { showEndIcon?: boolean },
) =>
  enqueueSnackbar(
    showEndIcon ? <CopyableSnackbarContent message={message} /> : message,
    {
      action: (snackbarId) => (
        <IconButton
          size="small"
          onClick={() => closeSnackbar(snackbarId)}
        >
          <CloseIcon
            fontSize="small"
            sx={{ color: 'white' }}
          />
        </IconButton>
      ),
      ...options,
    },
  )

export type EnqueueSnackbarWithCloseAction = typeof enqueueSnackbarWithCloseAction

export const enqueueSnackbarWithButtonAction = ({
  message,
  snackBarOptions,
  buttonAction,
  buttonText,
}: {
  message: SnackbarMessage
  snackBarOptions: RequireAtLeastOne<OptionsObject, 'variant'>
  buttonText: string
  buttonAction: () => void
  buttonProps?: Except<ButtonProps, 'onClick'>
}) =>
  enqueueSnackbar(message, {
    action: (snackbarId) => (
      <>
        <Button
          variant="text"
          color="inherit"
          onClick={() => {
            buttonAction()
            closeSnackbar(snackbarId)
          }}
        >
          {buttonText}
        </Button>
        <IconButton
          size="small"
          onClick={() => closeSnackbar(snackbarId)}
        >
          <CloseIcon
            fontSize="small"
            sx={{ color: 'white' }}
          />
        </IconButton>
      </>
    ),
    ...snackBarOptions,
  })

/**
 * @deprecated Import `enqueueSnackbarWithCloseAction` directly
 */
export const useSnackbarWithCloseAction = () =>
  useMemo(
    () => ({
      enqueueSnackbarWithCloseAction,
    }),
    [],
  )

export type UseSnackbarWithCloseActionReturnType = ReturnType<
  typeof useSnackbarWithCloseAction
>
