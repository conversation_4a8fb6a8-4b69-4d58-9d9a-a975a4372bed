import { createContext, useCallback, useMemo, useRef, useState } from 'react'
import { isEmpty, isNull, uniq } from 'lodash'
import {
  Box,
  Button,
  ContainerWithTabsForDataGrid,
  DataGridAsTabItem,
  GridActionsCellItem,
  GridToolbarStandardOld,
  LinearProgress,
  MenuItem,
  MenuList,
  Popover,
  Tooltip,
  Typography,
  useDataGridDateColumns,
  useSearchTextField,
  type GridColDef,
  type GridRowSelectionModel,
  type GridSingleSelectColDef,
} from '@karoo-ui/core'
import AccessTimeIcon from '@mui/icons-material/AccessTime'
import BuildIcon from '@mui/icons-material/Build'
import CalendarIcon from '@mui/icons-material/CalendarToday'
import CheckIcon from '@mui/icons-material/Check'
import EditOutlinedIcon from '@mui/icons-material/EditOutlined'
import InfoIcon from '@mui/icons-material/Info'
import ThreeDotsIcon from '@mui/icons-material/MoreVert'
import { DateTime } from 'luxon'
import PopupState, { bindPopover, bindTrigger } from 'material-ui-popup-state'
import { match } from 'ts-pattern'

import PageWithMainTableContainer from 'src/components/_containers/PageWithMainTable'
import { useModal } from 'src/hooks'
import { UserDataGridWithSavedSettingsOnIDB } from 'src/modules/components/connected/UserDataGridWithSavedSettingsOnIDB'
import {
  createDataGridNumberColumn,
  createDataGridTextColumn,
  type IntlGridColDef,
} from 'src/shared/data-grid/utils'
import type { FixMeAny } from 'src/types'

import { generateItemMatchesWithTextAndFilters, type Filters } from 'cartrack-utils'
import { ctIntl } from 'cartrack-ui-kit'
import AffectedBookingsWarningModal from './AffectedBookingsWarningModal'
import useCreateMaintenanceMutation from './api/useCreateMaintenanceMutation'
import useEditMaintenanceMutation from './api/useEditMaintenanceMutation'
import useMaintenanceAffectedBookingMutation, {
  type MaintenanceAffectedBookings,
} from './api/useMaintenanceAffectedBookingMutation'
import useMaintenanceListQuery, {
  type FetchMaintenanceList,
} from './api/useMaintenanceListQuery'
import useMaintenanceOptionsQuery, {
  type FetchMaintenanceOptions,
} from './api/useMaintenanceOptionsQuery'
import useStartMaintenanceMutation from './api/useStartMaintenanceMutation'
import EndMaintenanceModal from './EndMaintenanceModal'
import MaintenanceDetailsDrawer from './MaintenanceDetailsDrawer'
import ScheduleMaintenanceDrawer, {
  type ScheduleMaintenanceFormPossibleValues,
  type ScheduleMaintenanceFormValidSchema,
} from './ScheduleMaintenanceDrawer'

export const MAINTENANCE_STATUS_ID = {
  IN_SERVICE: '1',
  PENDING: '2',
  IN_MAINTENANCE: '3',
  UNDER_INSPECTION: '4',
  INSPECTION_FAILED: '5',
  INSPECTION_PASSED: '6',
  CANCELLED: '7',
}

const TABS = {
  REQUESTED: 'requested',
  BOOKED: 'booked',
  IN_MAINTENANCE: 'inMaintenance',
  UNDER_INSPECTION: 'underInspection',
  CLOSED: 'closed',
} as const

type VisibilityModel = {
  vehicle: boolean
  maintenanceType: boolean
  maintenancePartName: boolean
  reporter: boolean
  maintenanceStartDate: boolean
  maintenanceEndDate: boolean
  maintenanceLocation: boolean
  maintenanceReason: boolean
  actions: boolean
}

const TAB_OPTIONS = [
  {
    label: 'maintenance.status.table.tab.requested',
    value: TABS.REQUESTED,
  },
  {
    label: 'maintenance.status.table.tab.booked',
    value: TABS.BOOKED,
  },
  {
    label: 'maintenance.status.table.tab.inMaintenance',
    value: TABS.IN_MAINTENANCE,
  },
  {
    label: 'maintenance.status.table.tab.underInspection',
    value: TABS.UNDER_INSPECTION,
  },
  {
    label: 'maintenance.status.table.tab.closed',
    value: TABS.CLOSED,
  },
]

export const MaintenanceOptionsContext = createContext({
  maintenanceOptions: {} as FetchMaintenanceOptions.Return,
})

type DataGridRow = FetchMaintenanceList.Return['list'][number]

const MaintenanceList = () => {
  const { createDateTimeColumn, dateTimeColDefaultFormatter } = useDataGridDateColumns({
    filterMode: 'client',
  })

  const searchProps = useSearchTextField('')

  const createMaintenanceMutation = useCreateMaintenanceMutation()
  const editMaintenanceMutation = useEditMaintenanceMutation()
  const startMaintenanceMutation = useStartMaintenanceMutation()

  const [currentTab, setCurrentTab] = useState(TAB_OPTIONS[0].value)
  const [multiSelectedMaintenanceIds, setMultiSelectedMaintenanceIds] = useState<
    ReadonlySet<string>
  >(new Set())

  const columnsVisibilityBasedOnSelectedTab = (
    tab?: typeof currentTab,
  ): VisibilityModel => {
    switch (tab || currentTab) {
      case TABS.REQUESTED: {
        return {
          vehicle: true,
          maintenanceType: true,
          maintenancePartName: true,
          reporter: true,
          maintenanceStartDate: false,
          maintenanceEndDate: false,
          maintenanceLocation: false,
          maintenanceReason: true,
          actions: true,
        }
      }
      default: {
        return {
          vehicle: true,
          maintenanceType: true,
          maintenancePartName: true,
          reporter: true,
          maintenanceStartDate: true,
          maintenanceEndDate: true,
          maintenanceLocation: false,
          maintenanceReason: false,
          actions: true,
        }
      }
    }
  }

  const [columnVisibilityModel, setColumnVisibilityModel] = useState<VisibilityModel>(
    columnsVisibilityBasedOnSelectedTab,
  )

  const scheduleMaintenanceFormValues = useRef<{
    formValues: ScheduleMaintenanceFormPossibleValues
    isEdit: boolean
  } | null>(null)

  const [currentDrawer, setCurrentDrawer] = useState<
    | {
        type: 'schedule_maintenance'
        data: {
          initialValues: ScheduleMaintenanceFormPossibleValues
          maintenanceId: string
          maintenanceStatusId: string
          selectedVehicle: string | null
        } | null
      }
    | {
        type: 'maintenance_details'
        data: {
          maintenanceId: string
        }
      }
    | null
  >(null)

  const [isEndMaintenanceModalOpen, endMaintenanceModal] = useModal<{
    maintenanceIds: Array<string>
  }>(false)

  const [isAffectedBookingsWarningModalOpen, affectedBookingsWarningModal] =
    useModal(false)

  const maintenanceListQuery = useMaintenanceListQuery()
  const maintenanceOptionsQuery = useMaintenanceOptionsQuery()
  const maintenanceAffectedBookingMutation = useMaintenanceAffectedBookingMutation()

  const columnsGetters = useMemo(
    () =>
      ({
        maintenanceId: (i) => i.maintenanceId,
        maintenancePartName: (i) => i.maintenancePartName,
        maintenanceLocation: (i) => i.maintenanceLocation,
        maintenanceReason: (i) => i.maintenanceReason,
        maintenanceReasonId: (i) => i.maintenanceReasonId,
        maintenanceStartDate: (i) =>
          i.maintenanceStartDate ? new Date(i.maintenanceStartDate) : null,
        maintenanceEndDate: (i) =>
          i.maintenanceEndDate ? new Date(i.maintenanceEndDate) : null,
        maintenanceStatusId: (i) => i.maintenanceStatusId,
        maintenanceType: (i) => i.maintenanceTypeId,
        maintenanceTypeId: (i) => i.maintenanceTypeId,
        vehicle: (i) => i.vehicle,
        vehicleId: (i) => i.vehicleId,
        reporter: (i) => i.reporterName,
        operationId: (i) => i.operationId,
        requestDate: (i) => new Date(i.requestDate),
        maintenanceNotes: (i) => i.maintenanceNotes,
      }) satisfies Record<string, (i: DataGridRow) => unknown>,
    [],
  )

  const columns = useMemo(
    (): Array<GridColDef<DataGridRow>> => [
      createDateTimeColumn({
        field: 'requestDate',
        headerName: ctIntl.formatMessage({ id: 'maintenance.global.dateOfRequest' }),
        valueGetter: (_, row) => columnsGetters.requestDate(row),
      }),
      {
        field: 'vehicle',
        headerName: ctIntl.formatMessage({ id: 'Vehicle' }),
        valueGetter: (_, row) => columnsGetters.vehicle(row),
        flex: 1,
      },
      {
        type: 'singleSelect',
        field: 'maintenanceType',
        headerName: ctIntl.formatMessage({ id: 'Maintenance Type' }),
        valueGetter: (_, row) => columnsGetters.maintenanceType(row),
        valueOptions: maintenanceOptionsQuery.data
          ? maintenanceOptionsQuery.data.maintenanceTypes.map((options) => ({
              value: options.id,
              label: ctIntl.formatMessage({ id: options.typeDescription }),
            }))
          : [],

        flex: 1,
      } satisfies GridSingleSelectColDef<
        { value: string; label: string },
        DataGridRow,
        string
      >,
      {
        type: 'singleSelect',
        field: 'operationId',
        headerName: ctIntl.formatMessage({ id: 'maintenance.global.securityStatus' }),
        valueGetter: (_, row) => columnsGetters.operationId(row),
        valueOptions: maintenanceOptionsQuery.data
          ? maintenanceOptionsQuery.data.operationStates.map((options) => ({
              value: options.id,
              label: ctIntl.formatMessage({ id: options.description }),
            }))
          : [],

        flex: 1,
      } satisfies GridSingleSelectColDef<
        { value: string; label: string },
        DataGridRow,
        string
      >,
      {
        field: 'maintenancePartName',
        headerName: ctIntl.formatMessage({ id: 'maintenance.global.partName' }),
        valueGetter: (_, row) => columnsGetters.maintenancePartName(row),
        flex: 1,
      },
      {
        field: 'reporter',
        headerName: ctIntl.formatMessage({ id: 'maintenance.global.reporter' }),
        valueGetter: (_, row) => columnsGetters.reporter(row),
        flex: 1,
      },
      createDateTimeColumn({
        field: 'maintenanceStartDate',
        headerName: ctIntl.formatMessage({ id: 'Start Date' }),
        valueGetter: (_, row) => columnsGetters.maintenanceStartDate(row),
      }),
      createDateTimeColumn({
        field: 'maintenanceEndDate',
        headerName: ctIntl.formatMessage({ id: 'End Date' }),
        valueGetter: (_, row) => columnsGetters.maintenanceEndDate(row),
      }),
      {
        field: 'maintenanceLocation',
        headerName: ctIntl.formatMessage({ id: 'Location' }),
        valueGetter: (_, row) => columnsGetters.maintenanceLocation(row),
        flex: 1,
      },
      {
        field: 'maintenanceNotes',
        headerName: ctIntl.formatMessage({ id: 'Description' }),
        valueGetter: (_, row) => columnsGetters.maintenanceNotes(row),
        flex: 1,
      },
      {
        field: 'maintenanceReason',
        headerName: ctIntl.formatMessage({ id: 'Reason' }),
        valueGetter: (_, row) => columnsGetters.maintenanceReason(row),
        flex: 1,
      },
      ...getCustomFieldsColumns(maintenanceListQuery.data?.customFields),
      {
        field: 'actions',
        type: 'actions',
        headerName: ctIntl.formatMessage({ id: 'Actions' }),
        width: 120,
        align: 'right',
        getActions: ({ row }) => {
          const {
            vehicle: vehicleRegistration,
            vehicleId,
            maintenanceTypeId,
            maintenanceReasonId,
            maintenanceStartDate,
            maintenanceEndDate,
            maintenanceStatusId,
            maintenanceId,
            maintenancePartName,
            maintenanceNotes,
            operationId,
          } = row

          let actions: Array<FixMeAny> = []

          const initialValuesForEditMaintenance: ScheduleMaintenanceFormPossibleValues =
            {
              vehicleRegistration: vehicleId,
              type: maintenanceTypeId,
              reason: maintenanceReasonId,
              startTime: maintenanceStartDate ? new Date(maintenanceStartDate) : null,
              endTime: maintenanceEndDate ? new Date(maintenanceEndDate) : null,
              hasStartImmediately: isNull(maintenanceStartDate),
              isEndTimeUnknown: isNull(maintenanceEndDate),
              maintenancePartName: maintenancePartName,
              maintenanceNotes: maintenanceNotes,
              operationId,
            }

          if (
            [
              MAINTENANCE_STATUS_ID.PENDING,
              MAINTENANCE_STATUS_ID.IN_MAINTENANCE,
            ].includes(maintenanceStatusId)
          ) {
            actions = [
              <Tooltip
                title={ctIntl.formatMessage({ id: 'Edit' })}
                key="edit"
              >
                <GridActionsCellItem
                  icon={<EditOutlinedIcon />}
                  label={ctIntl.formatMessage({ id: 'Edit' })}
                  onClick={() =>
                    setCurrentDrawer({
                      type: 'schedule_maintenance',
                      data: {
                        initialValues: initialValuesForEditMaintenance,
                        maintenanceId: maintenanceId,
                        maintenanceStatusId: maintenanceStatusId,
                        selectedVehicle: vehicleRegistration,
                      },
                    })
                  }
                />
              </Tooltip>,
            ]

            if (maintenanceStatusId === MAINTENANCE_STATUS_ID.PENDING) {
              actions.push(
                <Tooltip
                  title={ctIntl.formatMessage({
                    id: 'maintenance.action.startMaintenance',
                  })}
                  key="start"
                >
                  <GridActionsCellItem
                    icon={maintenanceStartDate ? <BuildIcon /> : <AccessTimeIcon />}
                    label={ctIntl.formatMessage({ id: 'Start' })}
                    onClick={() => {
                      startMaintenanceMutation.mutate({
                        maintenanceIds: [maintenanceId] as Array<string>,
                      })
                    }}
                  />
                </Tooltip>,
              )
            }

            if (maintenanceStatusId === MAINTENANCE_STATUS_ID.IN_MAINTENANCE) {
              actions.push(
                <Tooltip
                  title={ctIntl.formatMessage({
                    id: 'list.maintenance.endMaintenance',
                  })}
                  key="endMaintenance"
                >
                  <GridActionsCellItem
                    icon={<CheckIcon />}
                    label=""
                    onClick={() =>
                      endMaintenanceModal.open({
                        maintenanceIds: [maintenanceId],
                      })
                    }
                  />
                </Tooltip>,
              )
            }

            actions.push(
              <Tooltip
                title={ctIntl.formatMessage({ id: 'Info' })}
                key="info"
              >
                <GridActionsCellItem
                  icon={<InfoIcon />}
                  label={ctIntl.formatMessage({ id: 'Info' })}
                  onClick={() =>
                    setCurrentDrawer({
                      type: 'maintenance_details',
                      data: {
                        maintenanceId: maintenanceId,
                      },
                    })
                  }
                />
              </Tooltip>,
            )
          }

          if (
            [
              MAINTENANCE_STATUS_ID.UNDER_INSPECTION,
              MAINTENANCE_STATUS_ID.INSPECTION_PASSED,
              MAINTENANCE_STATUS_ID.INSPECTION_FAILED,
              MAINTENANCE_STATUS_ID.CANCELLED,
            ].includes(maintenanceStatusId)
          ) {
            actions = [
              <Tooltip
                title={ctIntl.formatMessage({ id: 'Info' })}
                key="info"
              >
                <GridActionsCellItem
                  icon={<InfoIcon />}
                  label={ctIntl.formatMessage({ id: 'Info' })}
                  onClick={() =>
                    setCurrentDrawer({
                      type: 'maintenance_details',
                      data: {
                        maintenanceId: maintenanceId,
                      },
                    })
                  }
                />
              </Tooltip>,
            ]
          }

          return actions
        },
      },
    ],
    [
      createDateTimeColumn,
      columnsGetters,
      maintenanceListQuery.data,
      startMaintenanceMutation,
      endMaintenanceModal,
      maintenanceOptionsQuery.data,
    ],
  )

  const getCurrentDataSet = useCallback(() => {
    if (maintenanceListQuery.status === 'success') {
      return match(currentTab)
        .with(TABS.REQUESTED, () =>
          maintenanceListQuery.data.list.filter(
            (i) =>
              i.maintenanceStatusId === MAINTENANCE_STATUS_ID.PENDING &&
              !i.maintenanceStartDate,
          ),
        )
        .with(TABS.BOOKED, () =>
          maintenanceListQuery.data.list.filter(
            (i) =>
              i.maintenanceStatusId === MAINTENANCE_STATUS_ID.PENDING &&
              i.maintenanceStartDate,
          ),
        )
        .with(TABS.IN_MAINTENANCE, () =>
          maintenanceListQuery.data.list.filter(
            (i) => i.maintenanceStatusId === MAINTENANCE_STATUS_ID.IN_MAINTENANCE,
          ),
        )
        .with(TABS.UNDER_INSPECTION, () =>
          maintenanceListQuery.data.list.filter((i) =>
            [
              MAINTENANCE_STATUS_ID.UNDER_INSPECTION,
              MAINTENANCE_STATUS_ID.INSPECTION_FAILED,
            ].includes(i.maintenanceStatusId),
          ),
        )
        .with(TABS.CLOSED, () =>
          maintenanceListQuery.data.list.filter((i) =>
            [
              MAINTENANCE_STATUS_ID.INSPECTION_PASSED,
              MAINTENANCE_STATUS_ID.CANCELLED,
            ].includes(i.maintenanceStatusId),
          ),
        )
        .otherwise(() => [])
    }

    return []
  }, [currentTab, maintenanceListQuery.data?.list, maintenanceListQuery.status])

  const filteredData = useMemo(() => {
    const searchFilters: Filters<FetchMaintenanceList.Return['list'][number]> = {
      search: [
        columnsGetters.vehicle,
        (u) =>
          maintenanceOptionsQuery.data?.maintenanceTypes.find(
            (t: { id: string }) => t.id === u.maintenanceTypeId,
          )?.typeDescription,
        (u) =>
          maintenanceOptionsQuery.data?.operationStates.find(
            (t: { id: string }) => t.id === u.operationId,
          )?.description,
        (row) => {
          const date = columnsGetters.maintenanceStartDate(row)
          return date ? dateTimeColDefaultFormatter(date) : ''
        },
        (row) => {
          const date = columnsGetters.maintenanceEndDate(row)
          return date ? dateTimeColDefaultFormatter(date) : ''
        },
        columnsGetters.maintenanceLocation,
        columnsGetters.maintenanceReason,
        columnsGetters.maintenancePartName,
        columnsGetters.reporter,
        columnsGetters.maintenanceNotes,
      ],
    }

    const { itemMatchesWithTextAndFilters } = generateItemMatchesWithTextAndFilters(
      searchProps.value,
    )

    return getCurrentDataSet().filter((booking) =>
      itemMatchesWithTextAndFilters(booking, searchFilters),
    )
  }, [
    columnsGetters,
    searchProps.value,
    getCurrentDataSet,
    maintenanceOptionsQuery.data,
    dateTimeColDefaultFormatter,
  ])

  const submitCreateEditBooking = () => {
    if (!isEmpty(scheduleMaintenanceFormValues.current)) {
      const { formValues, isEdit } = scheduleMaintenanceFormValues.current as {
        formValues: ScheduleMaintenanceFormValidSchema
        isEdit: boolean
      }
      const {
        vehicleRegistration,
        type,
        maintenancePartName,
        reason,
        maintenanceNotes,
        startTime,
        endTime,
        hasStartImmediately,
        operationId,
      } = formValues

      if (
        isEdit &&
        currentDrawer?.type === 'schedule_maintenance' &&
        currentDrawer.data
      ) {
        editMaintenanceMutation.mutate(
          {
            maintenanceId: currentDrawer.data.maintenanceId,
            maintenanceType: type,
            maintenanceReason: reason,
            maintenancePartName: maintenancePartName,
            maintenanceNotes,
            startDate: startTime,
            endDate: endTime,
            maintenanceStartImmediately: hasStartImmediately,
            operationId: operationId,
          },
          {
            onSuccess() {
              setCurrentDrawer(null)
              affectedBookingsWarningModal.close()
            },
          },
        )
      } else {
        createMaintenanceMutation.mutate(
          {
            vehicleId: vehicleRegistration,
            maintenanceType: type,
            maintenanceReason: reason,
            startDate: startTime,
            endDate: endTime,
            maintenanceStartImmediately: hasStartImmediately,
            maintenancePartName: maintenancePartName,
            maintenanceNotes,
            operationId: operationId,
          },
          {
            onSuccess() {
              setCurrentDrawer(null)
              affectedBookingsWarningModal.close()
            },
          },
        )
      }
    }
  }

  const handleConfirmScheduleMaintenance = ({
    formValues,
    isEdit,
  }: {
    formValues: ScheduleMaintenanceFormPossibleValues
    isEdit: boolean
  }) => {
    scheduleMaintenanceFormValues.current = {
      formValues,
      isEdit,
    }

    maintenanceAffectedBookingMutation.mutate(
      {
        vehicleId: formValues.vehicleRegistration as string,
        startDate: formValues.startTime
          ? DateTime.fromJSDate(formValues.startTime).toFormat('yyyy-LL-dd')
          : null,
        endDate: formValues.endTime
          ? DateTime.fromJSDate(formValues.endTime).toFormat('yyyy-LL-dd')
          : null,
      },
      {
        onSuccess(data) {
          if (!isEmpty(data)) {
            affectedBookingsWarningModal.open()
          } else {
            submitCreateEditBooking()
          }
        },
      },
    )
  }

  const handleConfirmAffectedBookings = () => {
    submitCreateEditBooking()
  }

  const getBulkActionOptions = () => {
    if (isEmpty(multiSelectedMaintenanceIds)) {
      return []
    }

    const allStatus = uniq(
      (maintenanceListQuery.data?.list ?? [])
        .filter((m) => multiSelectedMaintenanceIds.has(columnsGetters.maintenanceId(m)))
        .map((m) => columnsGetters.maintenanceStatusId(m)),
    )

    if (
      allStatus.every((s) =>
        [MAINTENANCE_STATUS_ID.PENDING, MAINTENANCE_STATUS_ID.IN_MAINTENANCE].includes(
          s,
        ),
      )
    ) {
      return [
        <MenuItem
          key="end-maintenance"
          onClick={() =>
            endMaintenanceModal.open({
              maintenanceIds: [...multiSelectedMaintenanceIds],
            })
          }
        >
          {ctIntl.formatMessage({ id: 'list.maintenance.endMaintenance' })}
        </MenuItem>,
      ]
    }

    return []
  }

  const handleSelectionModelChange = (newSelectionModel: GridRowSelectionModel) => {
    setMultiSelectedMaintenanceIds(
      new Set([...newSelectionModel.ids].map((id) => id.toString())),
    )
  }

  const isLoadingWhenSubmit =
    createMaintenanceMutation.isPending ||
    editMaintenanceMutation.isPending ||
    maintenanceAffectedBookingMutation.isPending

  const shouldShowCheckboxSelection =
    currentTab === TABS.REQUESTED || currentTab === TABS.IN_MAINTENANCE

  const optionsContextValue = useMemo(
    () => ({
      maintenanceOptions:
        maintenanceOptionsQuery.data ?? ({} as FetchMaintenanceOptions.Return),
    }),
    [maintenanceOptionsQuery.data],
  )

  return (
    <MaintenanceOptionsContext.Provider value={optionsContextValue}>
      <PageWithMainTableContainer>
        <Box
          sx={{
            display: 'flex',
            justifyContent: 'space-between',
            mb: 4,
          }}
        >
          <Typography variant="h5">
            {ctIntl.formatMessage({ id: 'list.maintenance.header' })}
          </Typography>
          <Button
            size={'small'}
            startIcon={<CalendarIcon />}
            onClick={() =>
              setCurrentDrawer({
                type: 'schedule_maintenance',
                data: null,
              })
            }
            color="primary"
            variant="contained"
          >
            {ctIntl.formatMessage({ id: 'list.maintenance.scheduleMaintenance' })}
          </Button>
        </Box>

        <ContainerWithTabsForDataGrid
          renderTabs={() => (
            <ContainerWithTabsForDataGrid.Tabs
              value={currentTab}
              onChange={(_e, newValue) => {
                setCurrentTab(newValue)
                setColumnVisibilityModel(columnsVisibilityBasedOnSelectedTab(newValue))
              }}
            >
              {TAB_OPTIONS.map(({ label, value }) => (
                <ContainerWithTabsForDataGrid.Tab
                  key={value}
                  label={ctIntl.formatMessage({ id: label })}
                  value={value}
                  sx={{
                    height: '48px',
                    minHeight: '48px',
                  }}
                />
              ))}
            </ContainerWithTabsForDataGrid.Tabs>
          )}
        >
          <UserDataGridWithSavedSettingsOnIDB<
            FetchMaintenanceList.Return['list'][number]
          >
            Component={DataGridAsTabItem}
            dataGridId="vehiclesInMaintenance"
            disableVirtualization
            disableRowSelectionOnClick
            loading={maintenanceListQuery.isPending}
            autoPageSize
            pagination
            rows={filteredData}
            getRowId={(row) => row.maintenanceId}
            columns={columns}
            checkboxSelection={shouldShowCheckboxSelection}
            onRowSelectionModelChange={handleSelectionModelChange}
            rowSelectionModel={multiSelectedMaintenanceIds}
            columnVisibilityModel={columnVisibilityModel}
            onColumnVisibilityModelChange={(newModel) =>
              setColumnVisibilityModel(newModel as VisibilityModel)
            }
            slots={{ toolbar: GridToolbarStandardOld, loadingOverlay: LinearProgress }}
            slotProps={{
              toolbar: GridToolbarStandardOld.createProps({
                SearchTextFieldProps: searchProps,
                gridToolbarRightContent: shouldShowCheckboxSelection ? (
                  <PopupState
                    variant="popover"
                    popupId="bulk-action"
                  >
                    {(popupState) => (
                      <div>
                        <Button
                          size="small"
                          variant="outlined"
                          color="secondary"
                          sx={{
                            width: '30px',
                            height: '30px',
                            minWidth: 'auto',
                          }}
                          disabled={isEmpty(getBulkActionOptions())}
                          {...bindTrigger(popupState)}
                        >
                          <ThreeDotsIcon />
                        </Button>
                        <Popover
                          {...bindPopover(popupState)}
                          anchorOrigin={{
                            vertical: 'bottom',
                            horizontal: 'center',
                          }}
                          transformOrigin={{
                            vertical: 'top',
                            horizontal: 'center',
                          }}
                        >
                          <MenuList>
                            {getBulkActionOptions().map((item) => item)}
                          </MenuList>
                        </Popover>
                      </div>
                    )}
                  </PopupState>
                ) : null,
              }),
            }}
          />
        </ContainerWithTabsForDataGrid>

        {/* Modal */}
        {isEndMaintenanceModalOpen && (
          <EndMaintenanceModal
            onClose={() => endMaintenanceModal.close()}
            maintenanceIds={endMaintenanceModal.data?.maintenanceIds}
          />
        )}

        {isAffectedBookingsWarningModalOpen && (
          <AffectedBookingsWarningModal
            onClose={() => affectedBookingsWarningModal.close()}
            affectedBookingIds={
              !isEmpty(maintenanceAffectedBookingMutation.data)
                ? (
                    maintenanceAffectedBookingMutation.data as MaintenanceAffectedBookings.Return
                  ).map((i) => i.bookingId)
                : undefined
            }
            onConfirm={handleConfirmAffectedBookings}
            isLoading={
              editMaintenanceMutation.isPending || createMaintenanceMutation.isPending
            }
          />
        )}

        {/* Drawer */}
        {currentDrawer?.type === 'schedule_maintenance' && (
          <ScheduleMaintenanceDrawer
            onClose={() => setCurrentDrawer(null)}
            onConfirm={handleConfirmScheduleMaintenance}
            initialValues={currentDrawer.data?.initialValues}
            maintenanceId={currentDrawer.data?.maintenanceId}
            maintenanceStatusId={currentDrawer.data?.maintenanceStatusId}
            selectedVehicle={currentDrawer.data?.selectedVehicle}
            isLoadingWhenSubmit={isLoadingWhenSubmit}
          />
        )}

        {currentDrawer?.type === 'maintenance_details' && (
          <MaintenanceDetailsDrawer
            onClose={() => setCurrentDrawer(null)}
            maintenanceId={currentDrawer.data.maintenanceId}
          />
        )}
      </PageWithMainTableContainer>
    </MaintenanceOptionsContext.Provider>
  )
}

export default MaintenanceList

// create function to generate the columns for the custom fields
function getCustomFieldsColumns(
  customFields?: FetchMaintenanceList.Return['customFields'],
) {
  if (customFields === undefined) {
    return []
  }
  type Row = FetchMaintenanceList.Return['list'][number]
  type Value = string | number | null | undefined

  return Object.entries(customFields).map(
    ([key, customField]):
      | IntlGridColDef<
          Row,
          number | null | undefined,
          number | string | null | undefined
        >
      | IntlGridColDef<Row, string | null | undefined, string | null | undefined> => {
      const colDef: IntlGridColDef<Row, Value> = {
        field: key,
        headerName: customField.label,
        valueGetter: (_, row) => {
          if (row.customFields === undefined) {
            return null
          }

          return row.customFields[key as keyof typeof customFields] ?? null
        },
        flex: 1,
      }

      if (customField.type === 'number') {
        return createDataGridNumberColumn(
          colDef as IntlGridColDef<
            Row,
            number | null | undefined,
            number | string | null | undefined
          >,
        )
      } else {
        return createDataGridTextColumn(
          colDef as IntlGridColDef<
            Row,
            string | null | undefined,
            string | null | undefined
          >,
        )
      }
    },
  )
}
