import { useMemo, useState } from 'react'
import {
  Button,
  CircularProgressDelayedAbsolute,
  DataGrid,
  getGridSingleSelectOperators,
  GridActionsCellItem,
  gridStringOrNumberComparator,
  LinearProgress,
  Tooltip,
  Typography,
  type GridColDef,
  type GridRowSelectionModel,
  type GridSingleSelectColDef,
} from '@karoo-ui/core'
import AddIcon from '@mui/icons-material/Add'
import DevicesOtherIcon from '@mui/icons-material/DevicesOther'
import EditOutlinedIcon from '@mui/icons-material/EditOutlined'
import type { QueryObserverSuccessResult } from '@tanstack/react-query'
import { Link as RouterLink, useHistory } from 'react-router-dom'

import type { FacilityId } from 'api/types'
import { buildRouteQueryString } from 'api/utils'
import { getFacilitiesTranslatedModuleName, getFacilitiesTranslatorFn } from 'duxs/user'
import PageHeader from 'src/components/_containers/PageHeader'
import PageWithMainTableContainer from 'src/components/_containers/PageWithMainTable'
import ConfirmationModal from 'src/components/_modals/Confirmation'
import { useModal } from 'src/hooks'
import { LIST } from 'src/modules/app/components/routes/list'
import { UserDataGridWithSavedSettingsOnIDB } from 'src/modules/components/connected'
import { useTypedSelector } from 'src/redux-hooks'
import { DataGridDeleteButtonWithCounter } from 'src/shared/data-grid/DataGridDeleteButtonWithCounter'
import KarooToolbar from 'src/shared/data-grid/KarooToolbar'
import { createDataGridTextColumn } from 'src/shared/data-grid/utils'
import { formatDeleteDialogWithCountText } from 'src/util-functions/translation-utils'

import { ctIntl } from 'cartrack-ui-kit'
import {
  useDeleteFacilitiesMutation,
  useFacilitiesList,
  useFacilityTypes,
  type FacilitiesListResolved,
  type FetchFacilityTypesResolved,
} from './api/queries'
import type { FacilityTypeId } from './api/types'
import { assignDevicesSearchParamsSchema } from './AssignDevices'
import { facilityDetailsSearchParamsSchema } from './FacilityDetails'
import { mapFacilityTypeToTranslation } from './utils'

// const removeIsAnyFilterOperator = () =>
//   getGridSingleSelectOperators().filter((operator) => operator.value !== 'isAnyOf')

export function FacilitiesList() {
  const facilitiesListQuery = useFacilitiesList()
  const facilityTypesQuery = useFacilityTypes()

  if (
    facilityTypesQuery.status === 'pending' ||
    facilitiesListQuery.status === 'pending'
  ) {
    return <CircularProgressDelayedAbsolute />
  }

  if (facilityTypesQuery.status === 'error' || facilitiesListQuery.status === 'error') {
    return null
  }

  return (
    <Content
      successfulFacilitiesListQuery={facilitiesListQuery}
      facilityTypes={facilityTypesQuery.data.array}
    />
  )
}

type FacilityTypeAsValueOption = {
  value: FacilityTypeId
  label: string
}

function Content({
  successfulFacilitiesListQuery,
  facilityTypes,
}: {
  successfulFacilitiesListQuery: QueryObserverSuccessResult<FacilitiesListResolved>
  facilityTypes: FetchFacilityTypesResolved['array']
}) {
  const facilitiesTranslatedModuleName = useTypedSelector(
    getFacilitiesTranslatedModuleName,
  )

  const history = useHistory()
  const [rowsIdSelected, setRowsIdSelected] = useState<ReadonlySet<FacilityId>>(
    new Set(),
  )
  const [isDeleteModalOpen, deleteModalContext] = useModal(false)

  const deleteFacilitiesMutation = useDeleteFacilitiesMutation()

  const facilityTypesAsValueOptions = useMemo(
    () =>
      facilityTypes.map(
        (opt): FacilityTypeAsValueOption => ({
          value: opt.id,
          label: mapFacilityTypeToTranslation(opt.identifier),
        }),
      ),
    [facilityTypes],
  )

  const columnsGetters = useMemo(
    () =>
      ({
        name: (facility) => facility.name,
        address: (facility) => facility.address,
        type: (facility): FacilityTypeAsValueOption => ({
          value: facility.typeId,
          label: mapFacilityTypeToTranslation(facility.type),
        }),
        geofence: (facility) => facility.geofence?.name ?? '',
        coords: (facility) => `${facility.coords.lat} - ${facility.coords.lng}`,
        description: (facility) => facility.description ?? '',
        // status: (facility) => facility.status,
      }) satisfies Record<string, (item: FacilitiesListResolved[number]) => any>,
    [],
  )

  const filteredFacilities = useMemo(
    () => successfulFacilitiesListQuery.data,
    [successfulFacilitiesListQuery.data],
  )

  const columns = useMemo(
    (): Array<GridColDef<FacilitiesListResolved[number]>> => [
      createDataGridTextColumn({
        field: 'name',
        headerNameMsg: { id: 'Name' },
        valueGetter: (_, row) => columnsGetters.name(row),
        flex: 1,
      }),
      {
        type: 'singleSelect',
        field: 'type',
        headerName: 'Type',
        flex: 1,
        valueGetter: (_, row) => columnsGetters.type(row),
        valueFormatter: (value) => value.label,
        sortComparator: (v1, v2, param1, param2) =>
          gridStringOrNumberComparator(v1.label, v2.label, param1, param2),
        valueOptions: facilityTypesAsValueOptions,
        filterOperators: getGridSingleSelectOperators<
          FacilitiesListResolved[number],
          FacilityTypeAsValueOption,
          string
        >(),
      } satisfies GridSingleSelectColDef<
        FacilityTypeAsValueOption,
        FacilitiesListResolved[number],
        FacilityTypeAsValueOption,
        string
      >,
      createDataGridTextColumn({
        field: 'address',
        headerNameMsg: { id: 'Address' },
        valueGetter: (_, row) => columnsGetters.address(row),
        flex: 1,
      }),
      createDataGridTextColumn({
        field: 'geofence',
        headerNameMsg: { id: 'Geofence' },
        valueGetter: (_, row) => columnsGetters.geofence(row),
        flex: 1,
      }),
      createDataGridTextColumn({
        field: 'description',
        headerNameMsg: { id: 'Description' },
        valueGetter: (_, row) => columnsGetters.description(row),
        flex: 1,
      }),
      // {
      //   type: 'singleSelect',
      //   field: 'status',
      //   headerName: ctIntl.formatMessage({ id: 'Status' }),
      //   valueGetter: (_, row) => columnsGetters.status(row),
      //   renderCell: ({ row }) => (
      //     <StatusToggleComponent status={columnsGetters.status(row)} />
      //   ),
      //   valueOptions: [
      //     { value: true, label: ctIntl.formatMessage({ id: 'Enabled' }) },
      //     { value: false, label: ctIntl.formatMessage({ id: 'Disabled' }) },
      //   ],
      //   filterOperators: removeIsAnyFilterOperator(),
      // },
      {
        type: 'actions',
        field: 'actions',
        headerName: ctIntl.formatMessage({ id: 'Actions' }),
        getActions: (params) => [
          <Tooltip
            key="assignDevices"
            title={ctIntl.formatMessage({ id: 'locations.assignDevices' })}
            arrow
            disableInteractive
            placement="left"
          >
            <GridActionsCellItem
              icon={<DevicesOtherIcon />}
              label={ctIntl.formatMessage({ id: 'locations.assignDevices' })}
              onClick={() =>
                history.push(
                  `${
                    LIST.subMenusRoutes.FACILITIES.subPaths.ASSIGN_DEVICES
                  }?${buildRouteQueryString({
                    schema: assignDevicesSearchParamsSchema,
                    searchParams: {
                      facilityId: params.row.id,
                    },
                  })}`,
                )
              }
            />
          </Tooltip>,
          <Tooltip
            key="edit"
            title={ctIntl.formatMessage({ id: 'Edit' })}
            arrow
            placement="right"
            disableInteractive // Prevent tooltip from interfering with trying to click the other action cell items
          >
            <GridActionsCellItem
              icon={<EditOutlinedIcon />}
              label={ctIntl.formatMessage({ id: 'Edit' })}
              onClick={() =>
                history.push(
                  `${
                    LIST.subMenusRoutes.FACILITIES.subPaths.DETAILS
                  }?${buildRouteQueryString({
                    schema: facilityDetailsSearchParamsSchema,
                    searchParams: {
                      type: 'edit',
                      id: params.id as FacilityId,
                    },
                  })}`,
                )
              }
            />
          </Tooltip>,
        ],
      },
    ],
    [columnsGetters, history, facilityTypesAsValueOptions],
  )

  function handleSelectionModalChange(selection: GridRowSelectionModel) {
    setRowsIdSelected(
      new Set([...selection.ids].map((id) => id.toString() as FacilityId)),
    )
  }

  const { translateFacilitiesTerm } = useTypedSelector(getFacilitiesTranslatorFn)

  return (
    <PageWithMainTableContainer>
      <PageHeader>
        <PageHeader.Title>
          <Typography variant="h5">{facilitiesTranslatedModuleName}</Typography>
        </PageHeader.Title>
        <PageHeader.ButtonsContainer>
          <Button
            size="small"
            variant="contained"
            component={RouterLink}
            to={`${
              LIST.subMenusRoutes.FACILITIES.subPaths.DETAILS
            }?${buildRouteQueryString({
              schema: facilityDetailsSearchParamsSchema,
              searchParams: { type: 'add' },
            })}`}
            startIcon={<AddIcon />}
          >
            {translateFacilitiesTerm('facilities.addFacility')}
          </Button>
        </PageHeader.ButtonsContainer>
      </PageHeader>
      <UserDataGridWithSavedSettingsOnIDB
        checkboxSelection
        disableRowSelectionOnClick
        autoPageSize
        pagination
        rowSelectionModel={rowsIdSelected}
        onRowSelectionModelChange={handleSelectionModalChange}
        Component={DataGrid}
        dataGridId="LocationsList"
        rows={filteredFacilities}
        columns={columns}
        loading={successfulFacilitiesListQuery.isFetching}
        slots={{ toolbar: KarooToolbar, loadingOverlay: LinearProgress }}
        slotProps={{
          toolbar: KarooToolbar.createProps({
            slots: {
              searchFilter: { show: true },
              filterButton: { show: true },
              settingsButton: { show: true },
            },
            extraContent: {
              right: (
                <DataGridDeleteButtonWithCounter
                  count={rowsIdSelected.size}
                  ButtonProps={{
                    onClick: deleteModalContext.open,
                  }}
                />
              ),
            },
          }),
          basePagination: { material: { showFirstButton: true, showLastButton: true } },
        }}
      />

      {isDeleteModalOpen && (
        <ConfirmationModal
          title="facilities.deleteDialog.title"
          open
          onClose={deleteModalContext.close}
          onConfirm={() => {
            deleteFacilitiesMutation.mutate(
              { ids: [...rowsIdSelected] },
              {
                onSuccess() {
                  deleteModalContext.close()
                  setRowsIdSelected(new Set())
                },
              },
            )
          }}
          isLoading={deleteFacilitiesMutation.isPending}
        >
          {formatDeleteDialogWithCountText({
            count: rowsIdSelected.size,
            categories: {
              one: translateFacilitiesTerm('facilities.deleteDialog.areYouSure.one'),
              other: translateFacilitiesTerm(
                'facilities.deleteDialog.areYouSure.other',
              ),
            },
          })}
        </ConfirmationModal>
      )}
    </PageWithMainTableContainer>
  )
}

// const StatusToggleComponent = ({ status }: { status: boolean }) => {
//   const [state, setState] = useState(status)
//   return (
//     <Tooltip
//       arrow
//       placement="top"
//       title={ctIntl.formatMessage({ id: state ? 'Enabled' : 'Disabled' })}
//     >
//       {/* div necessary for tooltip to be positioned to
//           the center off the whole switch component & not to
//           the thumb off switch
//       */}
//       <div>
//         <Switch
//           size="small"
//           checked={state}
//           onChange={(_e, checked) => setState(checked)}
//         />
//       </div>
//     </Tooltip>
//   )
// }
