import { useMemo } from 'react'
import { size } from 'lodash'
import {
  DataGrid,
  GridActionsCellItem,
  Tooltip,
  useDataGridColumnHelper,
  type GridColDef,
} from '@karoo-ui/core'
import EditOutlinedIcon from '@mui/icons-material/EditOutlined'

import { getVehicleGroupLabel } from 'duxs/user-sensitive-selectors'
import {
  getVehicleGroups,
  getVehiclesLoading,
  type ReduxVehicleGroups,
} from 'duxs/vehicles'
import DataStatePlaceholder from 'src/components/_data/Placeholder'
import { useEventHandler } from 'src/hooks/useEventHandler'
import { UserDataGridWithSavedSettingsOnIDB } from 'src/modules/components/connected/UserDataGridWithSavedSettingsOnIDB'
import { useTypedSelector } from 'src/redux-hooks'
import KarooToolbar from 'src/shared/data-grid/KarooToolbar'

import { ctIntl, Stats } from 'cartrack-ui-kit'

type Props = {
  onRowClick: (row: ReduxVehicleGroups[number]) => void
  onRowEditIconClick: (row: ReduxVehicleGroups[number]) => void
}

export function VehicleGroupsTable({
  onRowClick,
  onRowEditIconClick: onRowEditIconClickProp,
}: Props) {
  const columnHelper = useDataGridColumnHelper<ReduxVehicleGroups[number]>({
    filterMode: 'client',
  })
  const vehicleGroups = useTypedSelector(getVehicleGroups)
  const vehiclesLoading = useTypedSelector(getVehiclesLoading)
  const { groupsLabel } = useTypedSelector(getVehicleGroupLabel)

  const onRowEditIconClick = useEventHandler(onRowEditIconClickProp)

  const groupColumns = useMemo(
    (): Array<GridColDef<ReduxVehicleGroups[number]>> => [
      columnHelper.string((_, row) => row.name, {
        headerName: ctIntl.formatMessage({ id: 'Name' }),
        field: 'name',
        flex: 1,
      }),
      columnHelper.number((_, row) => row.itemIds.length, {
        headerName: ctIntl.formatMessage({ id: 'Total Vehicles' }),
        field: 'itemIds',
        flex: 1,
        align: 'left',
        headerAlign: 'left',
      }),
      {
        field: 'actions',
        type: 'actions',
        headerName: ctIntl.formatMessage({ id: 'Actions' }),
        align: 'center',
        getActions: ({ row }) => [
          <Tooltip
            key="edit-groups"
            title={ctIntl.formatMessage({ id: 'Edit' })}
          >
            <GridActionsCellItem
              label={ctIntl.formatMessage({ id: 'Edit' })}
              icon={<EditOutlinedIcon />}
              onClick={() => {
                onRowEditIconClick(row)
              }}
            />
          </Tooltip>,
        ],
      },
    ],
    [columnHelper, onRowEditIconClick],
  )

  return (
    <>
      <UserDataGridWithSavedSettingsOnIDB
        Component={DataGrid}
        dataGridId="sc-vehicle-groups-list"
        data-testid="sc-vehicle-groups-list"
        rows={vehicleGroups}
        getRowId={(row) => row.groupId}
        columns={groupColumns}
        pagination
        pageSizeOptions={[25, 50, 100]}
        loading={vehiclesLoading}
        initialState={{
          pagination: {
            paginationModel: { pageSize: 25, page: 0 },
          },
        }}
        disableRowSelectionOnClick
        onRowClick={({ row }) => {
          onRowClick(row as (typeof vehicleGroups)[number])
        }}
        slots={{
          toolbar: KarooToolbar,
          noRowsOverlay: () => <DataStatePlaceholder label="No data available" />,
        }}
        slotProps={{
          toolbar: KarooToolbar.createProps({
            slots: {
              searchFilter: { show: true },
              settingsButton: { show: true },
            },
            extraContent: {
              middle: (
                <Stats
                  reversed
                  data={[
                    {
                      key: `Total ${groupsLabel}`,
                      value: size(vehicleGroups),
                    },
                  ]}
                />
              ),
            },
          }),
          basePagination: { material: { showFirstButton: true, showLastButton: true } },
        }}
        sx={{
          '& .MuiDataGrid-row': {
            cursor: 'pointer',
          },
        }}
      />
    </>
  )
}
