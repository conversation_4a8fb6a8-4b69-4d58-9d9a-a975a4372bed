import { useMemo, useState } from 'react'
import {
  DataGrid,
  GridActionsCellItem,
  LinearProgress,
  OverflowTypography,
  Tooltip,
  useDataGridColumnHelper,
  type GridColDef,
  type GridRowParams,
} from '@karoo-ui/core'
import EditIcon from '@mui/icons-material/EditOutlined'
import { match } from 'ts-pattern'

import { getFacilitiesTranslatorFn } from 'duxs/user'
import { getVehicleGroupLabel } from 'duxs/user-sensitive-selectors'
import PageHeader from 'src/components/_containers/PageHeader'
import PageWithMainTableContainer from 'src/components/_containers/PageWithMainTable'
import DataStatePlaceholder from 'src/components/_data/Placeholder'
import { UserDataGridWithSavedSettingsOnIDB } from 'src/modules/components/connected'
import { useTypedSelector } from 'src/redux-hooks'
import KarooToolbar from 'src/shared/data-grid/KarooToolbar'
import IntlTypography from 'src/util-components/IntlTypography'

import { ctIntl } from 'cartrack-ui-kit'
import type { FetchVehicleGroupsMapping } from './api/types'
import useVehicleGroupsMappingQuery from './api/useVehicleGroupsMappingQuery'
import VehicleGroupMappingEditDrawer from './VehicleGroupMappingEditDrawer'

type VehicleGroupMappingType =
  FetchVehicleGroupsMapping.Return['vehicleGroupsMapping'][number]

const VehicleGroupMapping = () => {
  const [editingVehicleGroupMapping, setEditingVehicleGroupMapping] = useState<
    VehicleGroupMappingType | undefined
  >(undefined)
  const { translateFacilitiesTerm } = useTypedSelector(getFacilitiesTranslatorFn)
  const { groupsLabel, groupLabel } = useTypedSelector(getVehicleGroupLabel)

  const vehicleGroupsMappingQuery = useVehicleGroupsMappingQuery()

  const columnHelper = useDataGridColumnHelper<VehicleGroupMappingType>({
    filterMode: 'client',
  })

  const columns = useMemo(
    (): Array<GridColDef<VehicleGroupMappingType>> => [
      columnHelper.string((_, row) => row.vehicleGroupName, {
        field: 'vehicleGroupName',
        headerName: ctIntl.formatMessage({
          id: groupLabel,
        }),
        flex: 1,
      }),
      columnHelper.string(
        (_, row) => (row.siteLocation ? row.siteLocation.siteLocationName : ''),
        {
          field: 'siteLocations',
          headerName: translateFacilitiesTerm('Facility'),
          flex: 1,
          renderCell: ({ value }) => (
            <OverflowTypography
              typographyProps={{
                variant: 'body2',
              }}
            >
              {value}
            </OverflowTypography>
          ),
        },
      ),
      {
        field: 'actions',
        type: 'actions',
        headerName: ctIntl.formatMessage({ id: 'Actions' }),
        align: 'center',
        getActions: ({ row }) => [
          <Tooltip
            key="edit-vehicle-groups-mapping"
            title={ctIntl.formatMessage({ id: 'Edit' })}
          >
            <GridActionsCellItem
              label={ctIntl.formatMessage({ id: 'Edit' })}
              icon={<EditIcon />}
              onClick={() => {
                setEditingVehicleGroupMapping(row)
              }}
            />
          </Tooltip>,
        ],
      },
    ],
    [columnHelper, groupLabel, translateFacilitiesTerm],
  )

  return (
    <>
      <PageWithMainTableContainer>
        <PageHeader>
          <PageHeader.Title>
            <IntlTypography
              msgProps={{
                id: `${groupsLabel} ${translateFacilitiesTerm('Facility')} Mapping`,
              }}
              variant="h5"
            />
          </PageHeader.Title>
        </PageHeader>
        {match(vehicleGroupsMappingQuery)
          .with(
            { status: 'success' },
            { status: 'pending' },
            ({ data, fetchStatus }) => (
              <UserDataGridWithSavedSettingsOnIDB
                Component={DataGrid}
                dataGridId="vehicle-groups-mapping"
                getRowId={(row) => row.vehicleGroupId}
                disableVirtualization
                disableRowSelectionOnClick
                onRowClick={({ row }: GridRowParams<VehicleGroupMappingType>) => {
                  setEditingVehicleGroupMapping(row)
                }}
                loading={fetchStatus === 'fetching'}
                autoPageSize
                slots={{
                  toolbar: KarooToolbar,
                  loadingOverlay: LinearProgress,
                  noRowsOverlay: () => (
                    <DataStatePlaceholder label={'No data available'} />
                  ),
                }}
                slotProps={{
                  toolbar: KarooToolbar.createProps({
                    slots: {
                      searchFilter: { show: true },
                      filterButton: { show: true },
                      settingsButton: { show: true },
                    },
                  }),
                }}
                columns={columns}
                rows={data?.vehicleGroupsMapping ?? []}
                pagination
              />
            ),
          )
          .with({ status: 'error' }, () => <DataStatePlaceholder type={'error'} />)
          .exhaustive()}
      </PageWithMainTableContainer>
      {!!editingVehicleGroupMapping && (
        <VehicleGroupMappingEditDrawer
          onClose={() => {
            setEditingVehicleGroupMapping(undefined)
          }}
          editingVehicleGroupMapping={editingVehicleGroupMapping}
          locations={vehicleGroupsMappingQuery.data?.locations ?? []}
        />
      )}
    </>
  )
}

export default VehicleGroupMapping
