import { use<PERSON><PERSON>back, useEffect, useMemo, useState } from 'react'
import {
  Button,
  Checkbox,
  DataGrid,
  GridActionsCellItem,
  GridRowModes,
  LinearProgress,
  TextField,
  Tooltip,
  useDataGridColumnHelper,
  useGridApiContext,
  useGridApiRef,
  type GridColDef,
  type GridRenderEditCellParams,
  type GridRowId,
  type GridRowModesModel,
} from '@karoo-ui/core'
import AddIcon from '@mui/icons-material/Add'
import CheckIcon from '@mui/icons-material/Check'
import ClearIcon from '@mui/icons-material/Clear'
import DeleteIcon from '@mui/icons-material/DeleteOutline'
import EditOutlinedIcon from '@mui/icons-material/EditOutlined'
import { useDispatch } from 'react-redux'
import { match } from 'ts-pattern'
import { z } from 'zod'

import { actions, listDataSelectors } from 'duxs/mifleet/list-data'
import { UserDataGridWithSavedSettingsOnIDB } from 'src/modules/components/connected'
import type { ListDataItem } from 'src/modules/mifleet/components/list-data/shared/types'
import { useTypedSelector } from 'src/redux-hooks'
import KarooToolbar from 'src/shared/data-grid/KarooToolbar'
import { messages } from 'src/shared/formik'
import type { FixMeAny } from 'src/types'
import { ctIntl } from 'src/util-components'
import { numberAsStringBasicSchema } from 'src/util-functions/zod-utils'

import { isTrue } from 'cartrack-utils'
import type {
  ListDataColumns,
  ListDataSchema,
  ListDataSettingType,
} from '../../data/list-data'

const { deleteListDataItem, fetchListDataItems, updateListDataItem } = actions

type DataGridRow = ListDataItem

type UnitCellValue = string | null

type CellExtraParams = {
  customParams?: {
    error: boolean
  }
}

const buildEditCellValidationSchema = (column: ListDataColumns) =>
  z.object({
    [column.field]: match(column.type)
      .with('string', () => z.string().min(1, { message: messages.required }))
      .with('boolean', () => z.boolean())
      .with('number', () =>
        numberAsStringBasicSchema.transform(Number).refine((val) => val >= 0, {
          message: messages.validPositiveNumber,
        }),
      )
      .otherwise(() => z.never()),
  })

const buildEditRowValidationSchema = (columns: ListDataSchema['columns']) => {
  let schema = z.object({})

  for (const column of columns) {
    const cellSchema = buildEditCellValidationSchema(column)

    schema = schema.merge(cellSchema)
  }

  return schema
}

type Props = {
  matchedListData: ListDataSettingType
}

export default function ListDataTable({ matchedListData }: Props) {
  const dispatch = useDispatch()
  const listItems = useTypedSelector(listDataSelectors.getItems)
  const isLoading = useTypedSelector(listDataSelectors.getLoading)
  const apiRef = useGridApiRef()

  const [rowItems, setRowItems] = useState<Array<DataGridRow>>([])
  const [rowModesModel, setRowModesModel] = useState<GridRowModesModel>({})

  const columnHelper = useDataGridColumnHelper({ filterMode: 'client' })

  const listDataSchema = matchedListData.schema

  useEffect(() => {
    dispatch(
      fetchListDataItems({
        setting: matchedListData,
      }),
    )
  }, [dispatch, matchedListData])

  useEffect(() => {
    setRowItems(listItems)
  }, [listItems])

  const handleDeleteItem = useCallback(
    (id: GridRowId) => {
      if (!apiRef.current) return
      const editingRow = apiRef.current.getRowWithUpdatedValues(id, '')

      dispatch(
        deleteListDataItem({ setting: matchedListData, itemToBeDeleted: editingRow }),
      )
    },
    [apiRef, dispatch, matchedListData],
  )

  const columns = useMemo(
    (): Array<GridColDef<DataGridRow>> => [
      ...listDataSchema.columns.map((column) =>
        match(column.type)
          .with(
            'string',
            'number',
            () =>
              columnHelper.string((_, row) => row[column.field], {
                field: column.field,
                headerName: ctIntl.formatMessage({ id: column.headerName }),
                flex: 1,
                editable: true,
                preProcessEditCellProps: (params) => {
                  const object = {
                    [column.field]: params.props.value,
                  }

                  const schema = buildEditCellValidationSchema(column)

                  const parsedResult = schema.safeParse(object, {
                    path: [`${column.field}`],
                  })

                  const extra: CellExtraParams = {
                    customParams: {
                      error: parsedResult.success === false,
                    },
                  }

                  return { ...params.props, ...extra }
                },
                renderEditCell: (params) => (
                  <EditCellString
                    column={column}
                    type={column.type}
                    {...params}
                  />
                ),
              }) as GridColDef<DataGridRow>,
          )
          .with(
            'boolean',
            () =>
              ({
                field: column.field,
                headerName: ctIntl.formatMessage({ id: column.headerName }),
                type: 'boolean',
                flex: 1,
                editable: true,
                valueGetter: (_, row) => isTrue(row[column.field]),
                renderCell: ({ row }) =>
                  isTrue(row[column.field]) ? (
                    <CheckIcon color="success" />
                  ) : (
                    <ClearIcon color="error" />
                  ),
                renderEditCell: ({ id, field, row }) => (
                  <Checkbox
                    color="primary"
                    checked={row[column.field]}
                    onChange={(e) => {
                      if (!apiRef.current) return
                      apiRef.current.setEditCellValue({
                        id,
                        field,
                        value: e.target.checked,
                      })
                    }}
                  />
                ),
              }) as GridColDef<DataGridRow>,
          )
          .exhaustive(),
      ),
      {
        field: 'actions',
        type: 'actions',
        width: 90,
        headerName: ctIntl.formatMessage({ id: 'Actions' }),
        getActions: ({ id }) => {
          const isInEditMode = rowModesModel[id]?.mode === GridRowModes.Edit

          return isInEditMode
            ? [
                <GridActionSaveButton
                  key="save"
                  listDataColumns={listDataSchema.columns}
                  rowId={id}
                  onClick={() => {
                    setRowModesModel((prev) => ({
                      ...prev,
                      [id]: { mode: GridRowModes.View },
                    }))
                  }}
                />,
                <Tooltip
                  key="cancel"
                  title={ctIntl.formatMessage({ id: 'Cancel' })}
                >
                  <span>
                    <GridActionsCellItem
                      icon={<ClearIcon color="error" />}
                      label={ctIntl.formatMessage({ id: 'Cancel' })}
                      onClick={() => {
                        setRowModesModel((prev) => ({
                          ...prev,
                          [id]: {
                            mode: GridRowModes.View,
                            ignoreModifications: true,
                          },
                        }))

                        setRowItems((prev) => prev.filter((i) => i?.flag !== true))
                      }}
                    />
                  </span>
                </Tooltip>,
              ]
            : [
                <Tooltip
                  key="edit"
                  title={ctIntl.formatMessage({ id: 'Edit' })}
                >
                  <span>
                    <GridActionsCellItem
                      icon={<EditOutlinedIcon />}
                      label={ctIntl.formatMessage({ id: 'Edit' })}
                      onClick={() => {
                        setRowModesModel((prev) => ({
                          ...prev,
                          [id]: { mode: GridRowModes.Edit },
                        }))
                      }}
                    />
                  </span>
                </Tooltip>,
                <Tooltip
                  key="delete"
                  title={ctIntl.formatMessage({ id: 'Delete' })}
                >
                  <span>
                    <GridActionsCellItem
                      icon={<DeleteIcon />}
                      label={ctIntl.formatMessage({ id: 'Delete' })}
                      onClick={() => handleDeleteItem(id)}
                    />
                  </span>
                </Tooltip>,
              ]
        },
      },
    ],
    [apiRef, columnHelper, handleDeleteItem, listDataSchema.columns, rowModesModel],
  )

  const rowUpdate = useCallback(
    (newRow: DataGridRow, oldRow: DataGridRow): Promise<DataGridRow> | DataGridRow =>
      new Promise((resolve) => {
        dispatch(
          updateListDataItem({
            setting: matchedListData,
            item: newRow,
            successCb: () => {
              resolve(newRow)
            },
            errorCb: () => {
              resolve(oldRow)
            },
          }),
        )
      }),
    [dispatch, matchedListData],
  )

  const handleAddNewRecord = () => {
    const newRow = {
      [matchedListData.schema.id]: 'newRow',
      flag: true,
    } as FixMeAny

    for (const column of listDataSchema.columns) {
      newRow[column.field] = column.type === 'boolean' ? false : ''
    }

    setRowItems((prev) => [newRow, ...prev])

    setRowModesModel((prev) => ({
      ...prev,
      newRow: { mode: GridRowModes.Edit },
    }))
  }

  return (
    <UserDataGridWithSavedSettingsOnIDB
      apiRef={apiRef}
      Component={DataGrid}
      dataGridId={`mifleet-${matchedListData.id}`}
      columns={columns}
      rows={rowItems}
      getRowId={(row) => row[matchedListData.schema.id]}
      sx={{
        '& .MuiDataGrid-row--editing': {
          boxShadow: 'none',
        },
        '& .MuiDataGrid-row--editing .MuiDataGrid-cell': {
          backgroundColor: 'rgba(244, 119, 53, 0.08)',
        },
      }}
      pagination
      pageSizeOptions={[10, 25, 50]}
      initialState={{
        pagination: {
          paginationModel: { pageSize: 10, page: 0 },
        },
      }}
      editMode="row"
      processRowUpdate={rowUpdate}
      rowModesModel={rowModesModel}
      loading={isLoading}
      slots={{
        toolbar: KarooToolbar,
        loadingOverlay: LinearProgress,
      }}
      slotProps={{
        toolbar: KarooToolbar.createProps({
          slots: {
            searchFilter: { show: true },
          },
          extraContent: {
            right: (
              <Button
                variant="outlined"
                size="small"
                startIcon={<AddIcon />}
                onClick={handleAddNewRecord}
              >
                {ctIntl.formatMessage({ id: listDataSchema.addButtonLabel })}
              </Button>
            ),
          },
        }),
        basePagination: { material: { showFirstButton: true, showLastButton: true } },
      }}
    />
  )
}

function EditCellString({
  column,
  value,
  id,
  field,
  customParams,
  type,
}: GridRenderEditCellParams<DataGridRow & FixMeAny, UnitCellValue> & CellExtraParams) {
  const apiRef = useGridApiContext()

  return (
    <TextField
      label={ctIntl.formatMessage({ id: column.headerName })}
      sx={{ width: '100%', backgroundColor: 'white', mx: 1 }}
      required
      error={customParams?.error}
      value={value ?? null}
      type={type}
      onChange={(e) => {
        const newValue: UnitCellValue = e.target.value

        apiRef.current.setEditCellValue({
          id,
          field,
          value: newValue,
        })
      }}
    />
  )
}

function GridActionSaveButton({
  rowId,
  listDataColumns,
  onClick,
}: {
  rowId: GridRowId
  listDataColumns: Array<ListDataColumns>
  onClick: () => void
}) {
  const apiRef = useGridApiContext()
  const editRowProps = apiRef.current.getRowWithUpdatedValues(rowId, '')
  const editRowValidationSchema = buildEditRowValidationSchema(listDataColumns)
  const object: Record<string, FixMeAny> = {}

  for (const column of listDataColumns) {
    object[column.field] = editRowProps[column.field]
  }

  const isFormValid = editRowValidationSchema.safeParse(object).success

  return (
    <Tooltip title={ctIntl.formatMessage({ id: 'Save' })}>
      <span>
        <GridActionsCellItem
          disabled={!isFormValid}
          icon={<CheckIcon color={isFormValid ? 'success' : 'disabled'} />}
          label={ctIntl.formatMessage({ id: 'Save' })}
          onClick={onClick}
        />
      </span>
    </Tooltip>
  )
}
