import { useMutation, useQueryClient } from '@tanstack/react-query'

import { makeMutationErrorHandlerWithSnackbar } from 'api/helpers'
import type { VehicleId } from 'api/types'
import { restDelete } from 'src/api/rest-api-caller'
import { enqueueSnackbarWithCloseAction } from 'src/components/Snackbar/Notistack/utils'
import { ctIntl } from 'src/util-components/ctIntl'

import { vehicleDocumentsQuery } from './useVehicleDocumentsQuery'

export declare namespace DeleteVehicleDocument {
  type ApiInput = {
    documentUrl: string
  }

  type ApiOutput = {
    value: {
      request: {
        uriPath: string
      }
      job: {
        id: string
        status: string
      }
    }
    Success: boolean
  }
}

export type DeleteVehicleDocumentParams = {
  vehicleId: VehicleId
  fileName: string
  documentUrl: string
}

async function deleteVehicleDocument(params: DeleteVehicleDocument.ApiInput) {
  return restDelete<DeleteVehicleDocument.ApiOutput>(`/file/delete`, {
    body: { fullUriPath: params.documentUrl },
  })
}

export default function useDeleteVehicleDocumentMutation() {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: ({ documentUrl }: DeleteVehicleDocumentParams) =>
      deleteVehicleDocument({ documentUrl }),
    onSuccess: (_, { vehicleId, fileName: documentName }) => {
      // Invalidate and refetch vehicle documents
      queryClient.invalidateQueries(vehicleDocumentsQuery(vehicleId))

      enqueueSnackbarWithCloseAction(
        ctIntl.formatMessage(
          { id: 'Document "{documentName}" deleted successfully' },
          { values: { documentName } },
        ),
        { variant: 'success', showEndIcon: false },
      )
    },
    ...makeMutationErrorHandlerWithSnackbar({
      nonStandardErrorHandler() {
        enqueueSnackbarWithCloseAction(
          ctIntl.formatMessage({ id: 'Failed to delete document' }),
          { variant: 'error' },
        )
      },
    }),
  })
}
