import { useC<PERSON>back, useMemo, useState } from 'react'
import {
  Button,
  DataGrid,
  IconButton,
  LinearProgress,
  Stack,
  Typography,
  useDataGridColumnHelper,
  useGridApiRef,
  type GridColDef,
} from '@karoo-ui/core'
import AddIcon from '@mui/icons-material/Add'
import DeleteOutlineOutlinedIcon from '@mui/icons-material/DeleteOutlineOutlined'
import FileDownloadOutlinedIcon from '@mui/icons-material/FileDownloadOutlined'
import saveAs from 'file-saver'
import { DateTime } from 'luxon'
import { match } from 'ts-pattern'

import { authUtils } from 'api/auth-utils'
import type { VehicleId } from 'api/types'
import DataStatePlaceholder from 'src/components/_data/Placeholder'
import ConfirmationModal from 'src/components/_modals/Confirmation'
import { UserDataGridWithSavedSettingsOnIDB } from 'src/modules/components/connected'
import KarooToolbar from 'src/shared/data-grid/KarooToolbar'
import { ctIntl } from 'src/util-components/ctIntl'

import useDeleteVehicleDocumentMutation from './api/useDeleteVehicleDocumentMutation'
import useVehicleDocumentsQuery, {
  type VehicleDocument,
} from './api/useVehicleDocumentsQuery'
import FileUploadModal from './components/FileUploadModal'
import { formatFileSize, getFileTypeIconFromFileExtension } from './utils/fileTypeUtils'

type Props = {
  vehicleId: VehicleId
}

export default function Documents({ vehicleId }: Props) {
  const apiRef = useGridApiRef()
  const [modalOpen, setModalOpen] = useState<
    { type: 'upload' } | { type: 'delete'; document: VehicleDocument } | null
  >(null)

  const documentsQuery = useVehicleDocumentsQuery(vehicleId)
  const deleteMutation = useDeleteVehicleDocumentMutation()

  const columnHelper = useDataGridColumnHelper<VehicleDocument>({
    filterMode: 'client',
  })

  const handleDelete = (document: VehicleDocument) => {
    setModalOpen({ type: 'delete', document })
  }

  const handleConfirmDelete = () => {
    if (modalOpen?.type !== 'delete') return

    deleteMutation.mutate(
      {
        vehicleId,
        fileName: modalOpen.document.fileName,
        documentUrl: modalOpen.document.downloadUrl,
      },
      {
        onSuccess: () => {
          setModalOpen(null)
        },
      },
    )
  }

  const handleDownload = useCallback(async (document: VehicleDocument) => {
    const accessToken = authUtils.getJwtAccessToken_USE_ONLY_ON_API_CALLERS()
    const fileUrl = new URL(document.downloadUrl || '')
    const response = await fetch(fileUrl, {
      method: 'GET',
      headers: accessToken ? { Authorization: 'Bearer ' + accessToken } : {},
    })

    const blob = await response.blob()

    // Create download link
    const url = window.URL.createObjectURL(blob)
    saveAs(url, document.fileName)
  }, [])

  const columns = useMemo(
    (): Array<GridColDef<VehicleDocument>> => [
      columnHelper.string((_, row) => row.fileName, {
        field: 'name',
        headerName: ctIntl.formatMessage({ id: 'Name' }),
        flex: 1,
        minWidth: 200,
        renderCell: ({ row }) => (
          <Stack
            direction="row"
            alignItems="center"
            spacing={1}
          >
            {getFileTypeIconFromFileExtension(row.fileExtension)}
            <Typography noWrap>{row.fileName}</Typography>
          </Stack>
        ),
      }),
      columnHelper.date({
        field: 'lastModified',
        headerName: ctIntl.formatMessage({ id: 'Last Modified' }),
        width: 180,
        valueGetter: (_, row) => row.lastModified,
        valueFormatter: (value) =>
          value ? DateTime.fromJSDate(value).toLocaleString() : '',
      }),
      columnHelper.number((_, row) => row.fileSize, {
        field: 'fileSize',
        headerName: ctIntl.formatMessage({ id: 'Size' }),
        width: 100,
        valueFormatter: (value) => formatFileSize(value as number),
      }),
      {
        field: 'actions',
        type: 'actions',
        headerName: ctIntl.formatMessage({ id: 'Actions' }),
        width: 120,
        getActions: ({ row }) => [
          <IconButton
            key="delete"
            size="small"
            onClick={() => handleDelete(row)}
            title={ctIntl.formatMessage({ id: 'Delete' })}
            disabled={deleteMutation.isPending}
          >
            <DeleteOutlineOutlinedIcon fontSize="small" />
          </IconButton>,
          <IconButton
            key="download"
            size="small"
            onClick={() => handleDownload(row)}
            title={ctIntl.formatMessage({ id: 'Download' })}
          >
            <FileDownloadOutlinedIcon fontSize="small" />
          </IconButton>,
        ],
      },
    ],
    [columnHelper, deleteMutation.isPending, handleDownload],
  )

  const documents = documentsQuery.data ?? []

  return (
    <>
      <UserDataGridWithSavedSettingsOnIDB
        dataGridId={`vehicle-documents-${vehicleId}`}
        data-testid={`vehicle-documents-${vehicleId}`}
        Component={DataGrid}
        apiRef={apiRef}
        rows={documents}
        columns={columns}
        loading={documentsQuery.isFetching}
        pagination
        pageSizeOptions={[10, 25, 50]}
        rowReordering
        disableRowSelectionOnClick
        initialState={{
          pagination: { paginationModel: { pageSize: 10, page: 0 } },
        }}
        slots={{
          toolbar: KarooToolbar,
          loadingOverlay: LinearProgress,
          noRowsOverlay: () => (
            <DataStatePlaceholder
              label={ctIntl.formatMessage({ id: 'No documents available' })}
            />
          ),
        }}
        slotProps={{
          toolbar: KarooToolbar.createProps({
            slots: { searchFilter: { show: true } },
            extraContent: {
              right: (
                <Button
                  variant="outlined"
                  size="small"
                  startIcon={<AddIcon />}
                  onClick={() => setModalOpen({ type: 'upload' })}
                >
                  {ctIntl.formatMessage({ id: 'ADD FILE' })}
                </Button>
              ),
            },
          }),
          basePagination: { material: { showFirstButton: true, showLastButton: true } },
        }}
      />
      {match(modalOpen)
        .with({ type: 'upload' }, () => (
          <FileUploadModal
            vehicleId={vehicleId}
            onClose={() => setModalOpen(null)}
          />
        ))
        .with({ type: 'delete' }, ({ document }) => (
          <ConfirmationModal
            title="Delete File"
            open
            confirmButtonLabel="Delete"
            onClose={() => {
              setModalOpen(null)
            }}
            onConfirm={() => {
              handleConfirmDelete()
            }}
            isLoading={deleteMutation.isPending}
          >
            {ctIntl.formatMessage(
              { id: 'Are you sure you want to delete "{fileName}"?' },
              { values: { fileName: document?.fileName ?? '' } },
            )}
          </ConfirmationModal>
        ))
        .with(null, () => null)
        .exhaustive()}
    </>
  )
}
