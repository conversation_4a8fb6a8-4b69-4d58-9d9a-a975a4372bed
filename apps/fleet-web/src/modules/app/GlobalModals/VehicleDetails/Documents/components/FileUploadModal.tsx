import { useCallback, useEffect, useState } from 'react'
import {
  Box,
  Button,
  CircularProgress,
  Dialog,
  DialogActions,
  DialogContent,
  DialogTitle,
  FormControlLabel,
  IconButton,
  Radio,
  RadioGroup,
  Stack,
  styled,
  Tooltip,
  Typography,
} from '@karoo-ui/core'
import CheckIcon from '@mui/icons-material/Check'
import DeleteOutlineOutlinedIcon from '@mui/icons-material/DeleteOutlineOutlined'
import ErrorIcon from '@mui/icons-material/Error'
import UploadFileIcon from '@mui/icons-material/UploadFile'
import VisibilityOutlinedIcon from '@mui/icons-material/VisibilityOutlined'
import { useQueryClient } from '@tanstack/react-query'
import { useDropzone } from 'react-dropzone'
import { FormattedMessage } from 'react-intl'
import { match } from 'ts-pattern'

import type { VehicleId } from 'api/types'
import OverflowableTextTooltip from 'src/components/_popups/Tooltip/OverflowableText'
import { enqueueSnackbarWithCloseAction } from 'src/components/Snackbar/Notistack/utils'
import ImagePreviewModal from 'src/modules/carpool/components/ImageModal'
import { ctIntl } from 'src/util-components/ctIntl'

import useJobStatusQuery from '../api/useJobStatusQuery'
import useUploadVehicleDocumentMutation from '../api/useUploadVehicleDocumentMutation'
import {
  vehicleDocumentsQuery,
  type VehicleDocument,
} from '../api/useVehicleDocumentsQuery'
import {
  formatFileSize,
  getFileTypeIcon,
  getSupportedFileExtensions,
  isViewableFile,
  MAX_FILE_SIZE,
  SUPPORTED_FILE_TYPES,
} from '../utils/fileTypeUtils'

type Props = {
  vehicleId: VehicleId
  existingDocuments: Array<VehicleDocument>
  onClose: () => void
}

type FileUploadStatus = {
  file: File
} & (
  | { status: 'pending' }
  | {
      status: 'uploading'
      jobId: number | null
      progress: number
    }
  | {
      status: 'completed' | 'failed'
      jobId: number
    }
)

const DropzoneContainer = styled(Box)<{
  isDragActive: boolean
}>(({ theme, isDragActive }) => ({
  border: `1px solid ${theme.palette.grey[300]}`,
  borderRadius: theme.shape.borderRadius,
  padding: theme.spacing(2),
  textAlign: 'center',
  cursor: 'pointer',
  backgroundColor: isDragActive ? theme.palette.action.hover : 'transparent',
  transition: 'all 0.2s ease',
  '&:hover': {
    backgroundColor: theme.palette.action.hover,
  },
}))

type DuplicateDialogState = {
  duplicateFiles: Array<{ file: File; existingFileName: string }>
  pendingFiles: Array<File>
}

type ActionTypes = 'replace' | 'keep'

export default function FileUploadModal({
  vehicleId,
  existingDocuments,
  onClose,
}: Props) {
  const queryClient = useQueryClient()
  const [fileStatuses, setFileStatuses] = useState<Array<FileUploadStatus>>([])
  const [previewImage, setPreviewImage] = useState<string | null>(null)
  const [duplicateDialog, setDuplicateDialog] = useState<DuplicateDialogState | null>(
    null,
  )
  const [duplicateAction, setDuplicateAction] = useState<ActionTypes>('replace')
  const supportedFormats = getSupportedFileExtensions()
  const uploadMutation = useUploadVehicleDocumentMutation()
  const isUploading = uploadMutation.isPending

  // Update file status based on job polling
  const updateFileStatus = (jobId: number, status: string, progress?: number) => {
    setFileStatuses((prev) =>
      prev.map((fs) => {
        if ('jobId' in fs && fs.jobId === jobId) {
          if (status === 'Completed') {
            return { ...fs, status: 'completed', progress: 100, jobId }
          } else if (status === 'Failed') {
            return { ...fs, status: 'failed', jobId }
          } else if (status === 'In Progress') {
            return { ...fs, progress: progress || 0, jobId }
          }
        }
        return fs
      }),
    )
  }

  const handleImagePreview = (previewUrl: string) => {
    setPreviewImage(previewUrl)
  }

  const handleClosePreview = () => {
    setPreviewImage(null)
  }

  const checkForDuplicateFile = (newFile: File): boolean =>
    fileStatuses.some(
      (fileStatus) =>
        fileStatus.file.name === newFile.name && fileStatus.file.size === newFile.size,
    )

  const checkForDuplicateWithExisting = (newFile: File): string | null => {
    const existingDoc = existingDocuments.find((doc) => doc.fileName === newFile.name)
    return existingDoc ? existingDoc.fileName : null
  }

  const { getRootProps, getInputProps, isDragActive, isDragAccept, isDragReject } =
    useDropzone({
      accept: SUPPORTED_FILE_TYPES,
      maxSize: MAX_FILE_SIZE,
      multiple: true,
      onDrop: (acceptedFiles) => {
        const newFiles: Array<FileUploadStatus> = []
        const duplicatesWithAddedFiles: Array<string> = []
        const duplicatesWithExisting: DuplicateDialogState['duplicateFiles'] = []

        for (const file of acceptedFiles) {
          if (checkForDuplicateFile(file)) {
            duplicatesWithAddedFiles.push(file.name)
          } else {
            const existingFileName = checkForDuplicateWithExisting(file)
            if (existingFileName) {
              duplicatesWithExisting.push({ file, existingFileName })
            } else {
              newFiles.push({ file, status: 'pending' })
            }
          }
        }

        if (duplicatesWithAddedFiles.length > 0) {
          enqueueSnackbarWithCloseAction(
            ctIntl.formatMessage(
              { id: 'File(s) already added: {fileNames}' },
              { values: { fileNames: duplicatesWithAddedFiles.join(', ') } },
            ),
            { variant: 'warning' },
          )
        }

        if (duplicatesWithExisting.length > 0) {
          setDuplicateDialog({
            duplicateFiles: duplicatesWithExisting,
            pendingFiles: newFiles.map((fs) => fs.file),
          })
        } else if (newFiles.length > 0) {
          setFileStatuses((prev) => [...prev, ...newFiles])
        }
      },
      onDropRejected: (rejectedFiles) => {
        // Handle rejected files - could show error messages
        console.warn('Rejected files:', rejectedFiles)
        enqueueSnackbarWithCloseAction(
          rejectedFiles[0].errors[0].code === 'file-invalid-type'
            ? 'Please upload the document with supported file formats: .jpeg, .jpg, .png, .gif, .doc, .docx, .xls, .xlsx, .pdf, .txt'
            : rejectedFiles[0].errors[0].message,
          { variant: 'error' },
        )
      },
    })

  const handleRemoveFile = (index: number) => {
    setFileStatuses((prev) => prev.filter((_, i) => i !== index))
  }

  const generateUniqueFileName = useCallback(
    (fileName: string): string => {
      const nameParts = fileName.split('.')
      const extension = nameParts.pop()
      const baseName = nameParts.join('.')

      let counter = 1
      let newFileName = `${baseName}-${counter}.${extension}`

      // Check against existing documents and current file statuses
      while (
        existingDocuments.some((doc) => doc.fileName === newFileName) ||
        fileStatuses.some((fs) => fs.file.name === newFileName)
      ) {
        counter++
        newFileName = `${baseName}-${counter}.${extension}`
      }

      return newFileName
    },
    [existingDocuments, fileStatuses],
  )

  const handleDuplicateConfirm = () => {
    if (!duplicateDialog) return

    const { duplicateFiles, pendingFiles } = duplicateDialog
    const newFiles: Array<FileUploadStatus> = []

    // Add pending files that don't have duplicates
    for (const file of pendingFiles) {
      newFiles.push({ file, status: 'pending' })
    }

    // Handle duplicate files based on user choice
    for (const { file } of duplicateFiles) {
      if (duplicateAction === 'replace') {
        // For replace, just add the file as is
        newFiles.push({ file, status: 'pending' })
      } else {
        // For keep both, rename the file
        const renamedFile = new File([file], generateUniqueFileName(file.name), {
          type: file.type,
          lastModified: file.lastModified,
        })
        newFiles.push({ file: renamedFile, status: 'pending' })
      }
    }

    setFileStatuses((prev) => [...prev, ...newFiles])
    setDuplicateDialog(null)
    setDuplicateAction('replace') // Reset to default
  }

  const handleDuplicateCancel = () => {
    setDuplicateDialog(null)
    setDuplicateAction('replace') // Reset to default
  }

  const handleUpload = useCallback(async () => {
    const pendingFiles = fileStatuses.filter((fs) => fs.status === 'pending')
    if (pendingFiles.length === 0) return

    // Set all pending files to uploading status
    setFileStatuses((prev) =>
      prev.map((fs) =>
        fs.status === 'pending'
          ? { ...fs, status: 'uploading', jobId: null, progress: 0 }
          : fs,
      ),
    )

    try {
      const files = pendingFiles.map((fs) => fs.file)
      const jobs = await uploadMutation.mutateAsync({ vehicleId, files })

      // Update file statuses with job information
      setFileStatuses((prev) =>
        prev.map((fs) => {
          if (fs.status === 'uploading') {
            const job = jobs.find((j) => j.fileName === fs.file.name)
            if (job) {
              if (job.status === 'Completed') {
                return { ...fs, status: 'completed', jobId: job.id }
              } else if (job.status === 'Failed') {
                return { ...fs, status: 'failed', jobId: job.id }
              } else {
                return {
                  ...fs,
                  status: 'uploading',
                  jobId: job.id,
                  progress: job.progress,
                }
              }
            }
          }
          return fs
        }),
      )
    } catch (error) {
      // Set all uploading files to failed status
      setFileStatuses((prev) =>
        prev.map((fs) =>
          fs.status === 'uploading'
            ? { file: fs.file, jobId: fs.jobId as number, status: 'failed' }
            : fs,
        ),
      )
      console.error('Upload failed:', error)
    }
  }, [fileStatuses, uploadMutation, vehicleId])

  const handleCancel = useCallback(() => {
    setFileStatuses([])

    // when close the modal, if any file is uploaded, refresh the document list
    const allCompleted = fileStatuses.some((fs) => fs.status === 'completed')

    if (allCompleted) {
      queryClient.invalidateQueries(vehicleDocumentsQuery(vehicleId))
    }

    onClose()
  }, [fileStatuses, onClose, queryClient, vehicleId])

  // Close modal when all files are completed successfully
  useEffect(() => {
    const allCompleted =
      fileStatuses.length > 0 && fileStatuses.every((fs) => fs.status === 'completed')

    if (allCompleted) {
      queryClient.invalidateQueries(vehicleDocumentsQuery(vehicleId))
      onClose()
    }
  }, [fileStatuses, queryClient, vehicleId, onClose])

  return (
    <Dialog
      open
      onClose={duplicateDialog ? handleDuplicateCancel : handleCancel}
      maxWidth="sm"
      fullWidth
    >
      {duplicateDialog ? (
        <>
          <DialogTitle>
            {ctIntl.formatMessage({ id: 'Upload duplicate file' })}
          </DialogTitle>
          <DialogContent>
            <Typography>
              {duplicateDialog.duplicateFiles.length === 1 ? (
                <FormattedMessage
                  id="<b>{fileName}</b> already exists. Do you want to replace existing file with a new version or keep both files?"
                  values={{
                    fileName: duplicateDialog.duplicateFiles[0].file.name,
                    b: (chunks) => <b>{chunks}</b>,
                  }}
                />
              ) : (
                <FormattedMessage
                  id="<b>{filesCount}</b> files already exist. Do you want to replace existing files with new versions or keep both files?"
                  values={{
                    filesCount: duplicateDialog.duplicateFiles.length,
                    b: (chunks) => <b>{chunks}</b>,
                  }}
                />
              )}
            </Typography>
            <RadioGroup
              value={duplicateAction}
              onChange={(e) => setDuplicateAction(e.target.value as ActionTypes)}
              sx={{ mt: 2 }}
            >
              <FormControlLabel
                value="replace"
                control={<Radio />}
                label={ctIntl.formatMessage({ id: 'Replace existing file' })}
              />
              <FormControlLabel
                value="keep"
                control={<Radio />}
                label={ctIntl.formatMessage({ id: 'Keep both files' })}
              />
            </RadioGroup>
          </DialogContent>
          <StyledDialogActions>
            <Button
              variant="outlined"
              color="secondary"
              onClick={handleDuplicateCancel}
            >
              {ctIntl.formatMessage({ id: 'Cancel' })}
            </Button>
            <Button
              variant="contained"
              onClick={handleDuplicateConfirm}
            >
              {ctIntl.formatMessage({ id: 'CONFIRM' })}
            </Button>
          </StyledDialogActions>
        </>
      ) : (
        <>
          <DialogContent>
            <Stack spacing={3}>
              <DropzoneContainer
                {...getRootProps()}
                isDragActive={isDragActive}
              >
                <input {...getInputProps()} />
                <UploadFileIcon
                  sx={{
                    fontSize: 24,
                    color: (() => {
                      if (isDragAccept) return 'success.main'
                      if (isDragReject) return 'error.main'
                      return 'action.active'
                    })(),
                  }}
                />
                <Typography gutterBottom>
                  {ctIntl.formatMessage({ id: 'Click to upload or drag and drop' })}
                </Typography>
                <Button
                  variant="outlined"
                  sx={{ mb: 2 }}
                >
                  {ctIntl.formatMessage({ id: 'BROWSE FILES' })}
                </Button>
                <Typography color="text.secondary">
                  {ctIntl.formatMessage(
                    { id: 'Supported file formats: {formats}' },
                    { values: { formats: supportedFormats } },
                  )}
                </Typography>
                <Typography color="text.secondary">
                  {ctIntl.formatMessage({ id: '(max. 3MB)' })}
                </Typography>
              </DropzoneContainer>

              {fileStatuses.length > 0 && (
                <Stack
                  spacing={1}
                  sx={{ maxHeight: 200, overflowY: 'auto' }}
                >
                  {fileStatuses.map((fileStatus, index) => (
                    <Box
                      key={`${fileStatus.file.name}-${index}`}
                      sx={(theme) => ({
                        display: 'flex',
                        alignItems: 'center',
                        justifyContent: 'space-between',
                        padding: theme.spacing(1, 0),
                      })}
                    >
                      <Stack
                        direction="row"
                        alignItems="center"
                        spacing={2}
                        width="70%"
                      >
                        {getFileTypeIcon(fileStatus.file.type)}
                        <Box width="100%">
                          <OverflowableTextTooltip>
                            {fileStatus.file.name}
                          </OverflowableTextTooltip>
                          <Typography
                            variant="caption"
                            color="text.secondary"
                          >
                            {formatFileSize(fileStatus.file.size)}
                          </Typography>
                        </Box>
                      </Stack>
                      <Stack
                        direction="row"
                        spacing={1}
                        alignItems="center"
                      >
                        {isViewableFile(fileStatus.file.type) && (
                          <IconButton
                            size="small"
                            onClick={() =>
                              handleImagePreview(URL.createObjectURL(fileStatus.file))
                            }
                          >
                            <VisibilityOutlinedIcon />
                          </IconButton>
                        )}
                        {match(fileStatus)
                          .with({ status: 'pending' }, () => (
                            <IconButton
                              size="small"
                              onClick={() => handleRemoveFile(index)}
                              disabled={isUploading}
                            >
                              <DeleteOutlineOutlinedIcon />
                            </IconButton>
                          ))
                          .with({ status: 'completed' }, () => (
                            <IconButton
                              size="small"
                              sx={{ color: 'success.main' }}
                            >
                              <CheckIcon />
                            </IconButton>
                          ))
                          .with({ status: 'failed' }, () => (
                            <Tooltip
                              title={ctIntl.formatMessage({ id: 'File Upload Failed' })}
                            >
                              <span>
                                <IconButton
                                  size="small"
                                  sx={{ color: 'error.main' }}
                                >
                                  <ErrorIcon />
                                </IconButton>
                              </span>
                            </Tooltip>
                          ))
                          .with({ status: 'uploading' }, (uploadingStatus) =>
                            uploadingStatus.jobId === null ? (
                              <IconButton size="small">
                                <CircularProgress size={20} />
                              </IconButton>
                            ) : (
                              <Box
                                sx={{ position: 'relative', display: 'inline-flex' }}
                              >
                                <CircularProgress
                                  variant="determinate"
                                  value={uploadingStatus.progress || 0}
                                  size={22}
                                />
                                <Box
                                  sx={{
                                    top: 0,
                                    left: 0,
                                    bottom: 0,
                                    right: 0,
                                    position: 'absolute',
                                    display: 'flex',
                                    alignItems: 'center',
                                    justifyContent: 'center',
                                  }}
                                >
                                  <Typography
                                    variant="caption"
                                    component="div"
                                    color="text.secondary"
                                    sx={{ fontSize: '0.6rem' }}
                                  >
                                    {Math.round(uploadingStatus.progress || 0)}
                                  </Typography>
                                </Box>
                              </Box>
                            ),
                          )
                          .exhaustive()}
                        {'jobId' in fileStatus &&
                          fileStatus.jobId &&
                          fileStatus.status === 'uploading' && (
                            <FileUploadStatusPoller
                              jobId={fileStatus.jobId}
                              onStatusUpdate={updateFileStatus}
                            />
                          )}
                      </Stack>
                    </Box>
                  ))}
                </Stack>
              )}
            </Stack>
          </DialogContent>

          <StyledDialogActions>
            <Button
              variant="outlined"
              color="secondary"
              onClick={handleCancel}
              disabled={isUploading}
            >
              {ctIntl.formatMessage({ id: 'Cancel' })}
            </Button>

            <Button
              variant="contained"
              onClick={handleUpload}
              disabled={
                fileStatuses.filter((fs) => fs.status === 'pending').length === 0 ||
                isUploading
              }
            >
              {isUploading
                ? ctIntl.formatMessage({ id: 'Uploading...' })
                : ctIntl.formatMessage({ id: 'Add File' })}
            </Button>
          </StyledDialogActions>
        </>
      )}

      {previewImage && (
        <ImagePreviewModal
          imageUrl={previewImage}
          onClose={handleClosePreview}
        />
      )}
    </Dialog>
  )
}

// Component to handle job status polling for individual files
type FileUploadStatusPollerProps = {
  jobId: number
  onStatusUpdate: (jobId: number, status: string, progress?: number) => void
}

function FileUploadStatusPoller({
  jobId,
  onStatusUpdate,
}: FileUploadStatusPollerProps) {
  const jobQuery = useJobStatusQuery(jobId)

  useEffect(() => {
    if (jobQuery.data) {
      const { status, completionLevel } = jobQuery.data
      onStatusUpdate(jobId, status, completionLevel)
    }
  }, [jobQuery.data, jobId, onStatusUpdate])

  return null
}

const StyledDialogActions = styled(DialogActions)(({ theme }) =>
  theme.unstable_sx({
    p: 2,
    gap: 1,
    justifyContent: 'space-between',
    borderTop: `1px solid ${theme.palette.divider}`,
  }),
)
