// Import SVG icons from assets
import { Box } from '@karoo-ui/core'
import { match } from 'ts-pattern'

import { makeSanitizedInnerHtmlProp } from 'src/util-functions/security-utils'

import CsvFileIcon from 'assets/svg/csv-file.svg'
import DocFileIcon from 'assets/svg/doc-file.svg'
import DocxFileIcon from 'assets/svg/docx-file.svg'
import ImgFileIcon from 'assets/svg/img-file.svg'
import OtherFileIcon from 'assets/svg/other-file.svg'
import PdfFileIcon from 'assets/svg/pdf-file.svg'
import TxtFileIcon from 'assets/svg/txt-file.svg'
import XlsFileIcon from 'assets/svg/xls-file.svg'
import XlsxFileIcon from 'assets/svg/xlsx-file.svg'

export function getFileTypeIcon(mimeType: string, size: 'small' | 'medium' = 'small') {
  const iconSize = size === 'small' ? 20 : 24

  const iconMaps = match(mimeType)
    .with('application/pdf', () => PdfFileIcon)
    .with('application/msword', () => DocFileIcon)
    .with(
      'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
      () => DocxFileIcon,
    )
    .with('application/vnd.ms-excel', () => XlsFileIcon)
    .with(
      'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
      () => XlsxFileIcon,
    )
    .with('text/plain', () => TxtFileIcon)
    .with('text/csv', () => CsvFileIcon)
    .with('image/jpeg', 'image/png', 'image/gif', () => ImgFileIcon)
    .otherwise(() => OtherFileIcon)

  return (
    <Box
      {...makeSanitizedInnerHtmlProp({ dirtyHtml: iconMaps })}
      sx={{ minWidth: iconSize, height: iconSize }}
    />
  )
}

const extensionToIcon: Record<string, any> = {
  jpg: ImgFileIcon,
  png: ImgFileIcon,
  gif: ImgFileIcon,
  pdf: PdfFileIcon,
  doc: DocFileIcon,
  docx: DocxFileIcon,
  xls: XlsFileIcon,
  xlsx: XlsxFileIcon,
  txt: TxtFileIcon,
}

export function getFileTypeIconFromFileExtension(extension: string) {
  return (
    <Box
      {...makeSanitizedInnerHtmlProp({
        dirtyHtml: extensionToIcon[extension.toLowerCase()] || OtherFileIcon,
      })}
      width={20}
      height={20}
    />
  )
}

export function formatFileSize(bytes: number): string {
  if (bytes === 0) return '0 Bytes'
  const k = 1024
  const sizes = ['Bytes', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  return parseFloat((bytes / Math.pow(k, i)).toFixed(0)) + ' ' + sizes[i]
}

export function isImageFile(mimeType: string): boolean {
  return mimeType.startsWith('image/')
}

export function isViewableFile(mimeType: string): boolean {
  // Only images are viewable in the browser for now
  return isImageFile(mimeType)
}

export const SUPPORTED_FILE_TYPES = {
  'image/jpeg': ['.jpeg', '.jpg'],
  'image/png': ['.png'],
  'image/gif': ['.gif'],
  'application/msword': ['.doc'],
  'application/vnd.openxmlformats-officedocument.wordprocessingml.document': ['.docx'],
  'application/vnd.ms-excel': ['.xls'],
  'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet': ['.xlsx'],
  'application/pdf': ['.pdf'],
  'text/plain': ['.txt'],
}

export const MAX_FILE_SIZE = 3 * 1024 * 1024 // 3MB

export function getSupportedFileExtensions(): string {
  return Object.values(SUPPORTED_FILE_TYPES).flat().join(', ')
}
