import { useC<PERSON>back, useMemo, useState } from 'react'
import {
  Box,
  Button,
  Chip,
  createDataGridBaseColumn,
  DataGrid,
  FormControl,
  GridActionsCellItem,
  GridRowModes,
  LinearProgress,
  MenuItem,
  Select,
  Stack,
  TextField,
  useGridApiContext,
  useSearchTextField,
  type GridColDef,
  type GridRenderEditCellParams,
  type GridRowId,
  type GridRowModesModel,
  type GridRowSelectionModel,
} from '@karoo-ui/core'
import AddIcon from '@mui/icons-material/Add'
import CancelIcon from '@mui/icons-material/Close'
import EditOutlinedIcon from '@mui/icons-material/EditOutlined'
import SaveIcon from '@mui/icons-material/Save'
import { mapValues } from 'remeda'
import { match, P } from 'ts-pattern'
import type { ReadonlyDeep } from 'type-fest'
import { useEffectReducer, type EffectReducer } from 'use-effect-reducer'
import { z } from 'zod'

import ArrowedTooltip from 'src/components/_popups/Tooltip/Arrowed'
import {
  getCarpoolAddRequestPurpose,
  getCarpoolDeleteRequestPurpose,
  getCarpoolEditRequestPurpose,
} from 'src/duxs/user-sensitive-selectors'
import { useModal } from 'src/hooks'
import { UserDataGridWithSavedSettingsOnIDB } from 'src/modules/components/connected'
import { useTypedSelector } from 'src/redux-hooks'
import { DataGridDeleteButtonWithCounter } from 'src/shared/data-grid/DataGridDeleteButtonWithCounter'
import KarooToolbar from 'src/shared/data-grid/KarooToolbar'
import { messages } from 'src/shared/formik'
import { ctIntl } from 'src/util-components/ctIntl'
import {
  generateItemMatchesWithTextAndFilters,
  type Filters,
} from 'src/util-functions/search-utils'
import { createZodObjPathGetter } from 'src/util-functions/zod-utils'

import useCarpoolOptionsQuery, {
  type VehicleType,
} from '../../queries/useCarpoolOptionsQuery'
import {
  useDeleteRequestPurposesMutation,
  useRequestPurposes,
  useUpdateRequestPurposeMutation,
  type RequestPurpose,
} from './api/queries'
import DeletePurposesModal from './DeletePurposesModal'
import NewRequestPurposeDrawer from './NewRequestPurposeDrawer'

type PurposeCellValue = string | null
type CellExtraParams = {
  customParams?: {
    error: boolean
  }
}

type VehicleTypesValue = Array<VehicleType> | null

const editRowValidationSchema = z.object({
  purpose: z.string().min(1, { message: messages.required }),
  allowedVehicleTypes: z
    .array(z.object({ id: z.number(), vehicleType: z.string() }))
    .min(1, { message: messages.required }),
})

type EditRowValidationSchema = z.infer<typeof editRowValidationSchema>

type ReducerState = ReadonlyDeep<{
  rowModesModel: GridRowModesModel
}>

type ReducerEvent =
  | {
      type: 'onRowSaveClick'
      id: GridRowId
    }
  | {
      type: 'onRowCancelClick'
      id: GridRowId
    }
  | {
      type: 'onRowEditClick'
      id: GridRowId
    }

type RowModel = RequestPurpose

export const createReducer =
  (): EffectReducer<ReducerState, ReducerEvent> => (state, event, _captureEffect) =>
    match([state, event])
      .with(
        [P.any, { type: 'onRowCancelClick' }],
        ([, { id }]): ReducerState => ({
          ...state,
          rowModesModel: {
            ...state.rowModesModel,
            [id]: { mode: GridRowModes.View, ignoreModifications: true },
          },
        }),
      )
      .with(
        [P.any, { type: 'onRowEditClick' }],
        ([, { id }]): ReducerState => ({
          ...state,
          rowModesModel: {
            ...mapValues(state.rowModesModel, (value, key) => {
              if (key === id) {
                return value
              }
              return { mode: GridRowModes.View, ignoreModifications: true }
            }),
            [id]: { mode: GridRowModes.Edit },
          },
        }),
      )
      .with(
        [P.any, { type: 'onRowSaveClick' }],
        ([, { id }]): ReducerState => ({
          ...state,
          rowModesModel: {
            ...state.rowModesModel,
            [id]: { mode: GridRowModes.View },
          },
        }),
      )
      .otherwise(() => state)

const RequestPurposes = () => {
  const searchProps = useSearchTextField('')
  const [newRequestDrawerOpen, newRequestDrawer] = useModal(false)
  const [isDeleteConfirmModalOpen, deleteConfirmModal] = useModal(false)
  const [selectedPurposes, setSelectedPurposes] = useState<ReadonlySet<GridRowId>>(
    new Set(),
  )
  const requestPurposesQuery = useRequestPurposes()
  const carpoolOptionsQuery = useCarpoolOptionsQuery()
  const updateRequestPurpose = useUpdateRequestPurposeMutation()
  const deleteRequestPurposes = useDeleteRequestPurposesMutation()

  const canAdd = useTypedSelector(getCarpoolAddRequestPurpose)
  const canEdit = useTypedSelector(getCarpoolEditRequestPurpose)
  const canDelete = useTypedSelector(getCarpoolDeleteRequestPurpose)

  const isMutating = updateRequestPurpose.isPending || deleteRequestPurposes.isPending

  const columnsGetters = useMemo(
    () => ({
      purpose: (r: RequestPurpose) => r.purpose,
      allowedVehicleTypes: (r: RequestPurpose) => r.allowedVehicleTypes,
    }),
    [],
  )

  const processRowUpdate = useCallback(
    (newRow: RowModel, oldRow: RowModel): Promise<RowModel> | RowModel =>
      new Promise((resolve, _reject) => {
        const rollbackChanges = () => resolve(oldRow)
        updateRequestPurpose.mutate(
          {
            id: newRow.id,
            purpose: newRow.purpose,
            vehicleTypeIds: newRow.allowedVehicleTypes.map((t) => t.id),
          },
          {
            onSuccess() {
              resolve(newRow)
            },
            onError() {
              rollbackChanges()
            },
          },
        )
      }),
    [updateRequestPurpose],
  )

  const filteredRequestPurposes = useMemo((): Array<RowModel> => {
    if (requestPurposesQuery.data === undefined) {
      return []
    }
    const searchFilters: Filters<RequestPurpose> = {
      search: [
        columnsGetters.purpose,
        (r: RequestPurpose) =>
          r.allowedVehicleTypes.map((t) => t.vehicleType).join(', '),
      ],
    }
    const { itemMatchesWithTextAndFilters } = generateItemMatchesWithTextAndFilters(
      searchProps.value,
    )
    return requestPurposesQuery.data.filter((request: RequestPurpose) =>
      itemMatchesWithTextAndFilters(request, searchFilters),
    )
  }, [columnsGetters, searchProps.value, requestPurposesQuery.data])

  const [{ rowModesModel }, dispatch] = useEffectReducer(
    createReducer(),
    (): ReducerState => ({ rowModesModel: {} }),
  )

  const columns = useMemo((): Array<GridColDef<RequestPurpose>> => {
    const vehicleTypes =
      carpoolOptionsQuery.data?.vehicleTypes ?? ([] as Array<VehicleType>)
    const allColumns: Array<GridColDef<RequestPurpose>> = [
      createDataGridBaseColumn<RequestPurpose, string>({
        field: 'purpose',
        headerName: ctIntl.formatMessage({ id: 'carpool.requestPurposes.purpose' }),
        valueGetter: (_, row) => columnsGetters.purpose(row),
        flex: 1,
        editable: true,
        renderEditCell: (params) => <PurposeEditCell {...params} />,
        preProcessEditCellProps: (params) => {
          if (params.otherFieldsProps === undefined) {
            return params.props
          }

          const object: EditRowValidationSchema = {
            purpose: params.props.value,
            allowedVehicleTypes: params.otherFieldsProps.allowedVehicleTypes.value,
          }
          const parseResult = editRowValidationSchema.safeParse(object, {
            path: createZodObjPathGetter(object).createPath(['purpose']),
          })

          const extra: CellExtraParams = {
            customParams: {
              error: parseResult.success === false,
            },
          }
          return { ...params.props, ...extra }
        },
        valueSetter: (value, row) => ({
          ...row,
          purpose: value,
        }),
      }),
      createDataGridBaseColumn<
        RequestPurpose,
        Array<{
          id: number
          vehicleType: string
        }>
      >({
        field: 'allowedVehicleTypes',
        headerName: ctIntl.formatMessage({
          id: 'carpool.requestPurposes.allowedVehicleTypes',
        }),
        valueGetter: (_, row) => columnsGetters.allowedVehicleTypes(row),
        flex: 1,
        editable: true,
        renderCell: (params) =>
          columnsGetters
            .allowedVehicleTypes(params.row)
            .map((t) => t.vehicleType)
            .join(', '),
        renderEditCell: (params) => (
          <AlowedVehicleTypeEditCell
            {...params}
            vehicleTypes={vehicleTypes}
          />
        ),
        preProcessEditCellProps: (params) => {
          if (params.otherFieldsProps === undefined) {
            return params.props
          }

          const object: EditRowValidationSchema = {
            purpose: params.otherFieldsProps.purpose.value,
            allowedVehicleTypes: params.props.value,
          }
          const parseResult = editRowValidationSchema.safeParse(object, {
            path: createZodObjPathGetter(object).createPath(['allowedVehicleTypes']),
          })

          const extra: CellExtraParams = {
            customParams: {
              error: parseResult.success === false,
            },
          }
          return { ...params.props, ...extra }
        },
        valueSetter: (value, row) => ({
          ...row,
          allowedVehicleTypes: value,
        }),
      }),
      {
        field: 'actions',
        type: 'actions',
        headerName: 'Actions',
        getActions: ({ id }) => {
          const isInEditMode = rowModesModel[id]?.mode === GridRowModes.Edit
          if (isInEditMode) {
            return [
              <GridActionSaveButton
                rowId={id}
                key="save"
                onClick={() => dispatch({ type: 'onRowSaveClick', id })}
              />,
              <GridActionsCellItem
                key="cancel"
                icon={<CancelIcon />}
                label={ctIntl.formatMessage({ id: 'Cancel' })}
                onClick={() => dispatch({ type: 'onRowCancelClick', id })}
                color="inherit"
              />,
            ]
          }

          return [
            <ArrowedTooltip
              key="edit"
              label={ctIntl.formatMessage({
                id: 'Edit',
              })}
            >
              <span>
                <GridActionsCellItem
                  disabled={isMutating || !canEdit}
                  icon={<EditOutlinedIcon />}
                  label={ctIntl.formatMessage({ id: 'Edit' })}
                  onClick={() => dispatch({ type: 'onRowEditClick', id })}
                  color="inherit"
                />
              </span>
            </ArrowedTooltip>,
          ]
        },
      },
    ]

    return allColumns.filter((c) => c.field !== 'actions' || canEdit)
  }, [
    carpoolOptionsQuery.data?.vehicleTypes,
    columnsGetters,
    rowModesModel,
    isMutating,
    canEdit,
    dispatch,
  ])

  const handleSelectionModelChange = (selectionModal: GridRowSelectionModel) => {
    setSelectedPurposes(selectionModal.ids)
  }

  const handleDeletePurposes = () => {
    deleteRequestPurposes.mutate(
      { ids: [...selectedPurposes].map(Number) },
      {
        onSuccess: () => {
          setSelectedPurposes(new Set())
        },
      },
    )
  }

  return (
    <Box
      sx={{
        display: 'flex',
        flexFlow: 'column',
        height: '100%',
        width: '100%',
        gap: 2,
      }}
    >
      <UserDataGridWithSavedSettingsOnIDB
        dataGridId="requestPurpose"
        Component={DataGrid}
        sx={{ '& .MuiDataGrid-row': { cursor: 'pointer' } }}
        checkboxSelection={canDelete}
        disableRowSelectionOnClick
        rowSelectionModel={selectedPurposes}
        onRowSelectionModelChange={handleSelectionModelChange}
        editMode="row"
        disableVirtualization
        disableClipboardPaste
        autoPageSize
        loading={requestPurposesQuery.isFetching || isMutating}
        slots={{
          toolbar: KarooToolbar,
          loadingOverlay: LinearProgress,
        }}
        slotProps={{
          toolbar: KarooToolbar.createProps({
            slots: {
              searchFilter: { show: true, props: { quickSearchDebounceMs: 500 } },
              settingsButton: { show: true },
              filterButton: { show: true },
            },
            extraContent: {
              right: (
                <Stack
                  direction="row"
                  spacing={1}
                >
                  {canAdd && (
                    <Button
                      variant="contained"
                      onClick={() => {
                        newRequestDrawer.open()
                      }}
                      size="small"
                      startIcon={<AddIcon />}
                    >
                      {ctIntl.formatMessage({ id: 'Add New' })}
                    </Button>
                  )}
                  {canDelete && (
                    <DataGridDeleteButtonWithCounter
                      count={selectedPurposes.size}
                      ButtonProps={{
                        onClick: deleteConfirmModal.open,
                      }}
                    />
                  )}
                </Stack>
              ),
            },
          }),
          basePagination: { material: { showFirstButton: true, showLastButton: true } },
        }}
        columns={columns}
        rows={filteredRequestPurposes}
        pagination
        processRowUpdate={processRowUpdate}
        onRowEditStart={(_, event) => {
          // eslint-disable-next-line no-param-reassign
          event.defaultMuiPrevented = true
        }}
        onRowEditStop={(_, event) => {
          // eslint-disable-next-line no-param-reassign
          event.defaultMuiPrevented = true
        }}
        onCellEditStart={(_, event) => {
          // eslint-disable-next-line no-param-reassign
          event.defaultMuiPrevented = true
        }}
        rowModesModel={rowModesModel}
      />

      {newRequestDrawerOpen && (
        <NewRequestPurposeDrawer
          onClose={() => newRequestDrawer.close()}
          vehicleTypes={
            carpoolOptionsQuery.data?.vehicleTypes ?? ([] as Array<VehicleType>)
          }
        />
      )}

      {isDeleteConfirmModalOpen && (
        <DeletePurposesModal
          onClose={() => deleteConfirmModal.close()}
          onConfirm={() => {
            handleDeletePurposes()
            deleteConfirmModal.close()
          }}
        />
      )}
    </Box>
  )
}

export default RequestPurposes

function PurposeEditCell({
  id,
  value,
  field,
  customParams,
}: GridRenderEditCellParams<RequestPurpose, PurposeCellValue> & CellExtraParams) {
  const apiRef = useGridApiContext()
  return (
    <TextField
      sx={{
        ml: 2,
        mr: 2,
      }}
      fullWidth
      error={customParams?.error}
      required
      value={value ?? null}
      onChange={(e) => {
        const newValue: PurposeCellValue = e.target.value
        apiRef.current.setEditCellValue({
          id,
          field,
          value: newValue,
        })
      }}
      onClick={(e) => {
        e.stopPropagation()
      }}
    />
  )
}

function AlowedVehicleTypeEditCell({
  id,
  value,
  field,
  vehicleTypes,
  customParams,
}: GridRenderEditCellParams<RequestPurpose, VehicleTypesValue> &
  CellExtraParams & { vehicleTypes: Array<VehicleType> }) {
  const apiRef = useGridApiContext()
  return (
    <FormControl
      sx={{ ml: 1, mr: 1, width: '100%' }}
      size="small"
    >
      <Select
        sx={{ height: '40px' }}
        multiple
        value={value?.map((t) => t.id) ?? ([] as Array<number>)}
        error={customParams?.error}
        onChange={(e) => {
          const newValue = e.target.value as Array<number>
          apiRef.current.setEditCellValue({
            id,
            field,
            value: vehicleTypes.filter((t) => newValue.includes(t.id)),
          })
        }}
        renderValue={(selectedTypeIds) => (
          <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 0.5 }}>
            {vehicleTypes
              .filter((t) => selectedTypeIds.includes(t.id))
              .map((type: VehicleType) => (
                <Chip
                  key={type.id}
                  label={type.vehicleType}
                  onMouseDown={(e) => {
                    e.stopPropagation()
                  }}
                  onDelete={() => {
                    apiRef.current.setEditCellValue({
                      id,
                      field,
                      value: (value ?? []).filter((t) => t.id !== type.id),
                    })
                  }}
                />
              ))}
          </Box>
        )}
      >
        {vehicleTypes.map((type) => (
          <MenuItem
            key={type.id}
            value={type.id}
          >
            {type.vehicleType}
          </MenuItem>
        ))}
      </Select>
    </FormControl>
  )
}

export function GridActionSaveButton({
  rowId,
  onClick,
}: {
  rowId: GridRowId
  onClick: () => void
}) {
  const apiRef = useGridApiContext()
  const editRowProps = apiRef.current.getRowWithUpdatedValues(rowId, '') as RowModel

  const object: EditRowValidationSchema = {
    purpose: editRowProps.purpose,
    allowedVehicleTypes: editRowProps.allowedVehicleTypes,
  }
  const isFormValid = editRowValidationSchema.safeParse(object).success

  return (
    <GridActionsCellItem
      disabled={!isFormValid}
      icon={<SaveIcon />}
      label={ctIntl.formatMessage({ id: 'Save' })}
      color="primary"
      onClick={onClick}
    />
  )
}
