import { useMemo, useState } from 'react'
import { isEmpty } from 'lodash'
import {
  Autocomplete,
  Box,
  DataGrid,
  GridActionsCellItem,
  LinearProgress,
  Switch,
  TextField,
  Typography,
  useSearchTextField,
  type GridColDef,
} from '@karoo-ui/core'
import CheckIcon from '@mui/icons-material/Check'
import CloseIcon from '@mui/icons-material/Close'
import EditOutlinedIcon from '@mui/icons-material/EditOutlined'
import { match, P } from 'ts-pattern'
import { z } from 'zod'

import { getSpecialLicensesDynamicFeatureName } from 'duxs/user'
import { getCarpoolEditRules } from 'src/duxs/user-sensitive-selectors'
import { UserDataGridWithSavedSettingsOnIDB } from 'src/modules/components/connected'
import { useTypedSelector } from 'src/redux-hooks'
import KarooToolbar from 'src/shared/data-grid/KarooToolbar'
import { messages } from 'src/shared/formik'
import { ctIntl } from 'src/util-components/ctIntl'
import {
  generateItemMatchesWithTextAndFilters,
  type Filters,
} from 'src/util-functions/search-utils'

import { mapRulesToTranslationKeys } from '../../utils/helpers'
import {
  RULE_TYPES,
  RULES,
  useKeyCollectionRule,
  useRuleMutation,
  useRules,
  type ActiveBookingType,
  type Rule,
} from './api/queries'

type unitDropDownValueType = 'minutes' | 'hours' | 'days' | 'months'

const unitDropDownOptions: Array<{ label: string; value: unitDropDownValueType }> = [
  { label: ctIntl.formatMessage({ id: 'Minutes' }), value: 'minutes' },
  { label: ctIntl.formatMessage({ id: 'Hours' }), value: 'hours' },
  { label: ctIntl.formatMessage({ id: 'Days' }), value: 'days' },
  { label: ctIntl.formatMessage({ id: 'Months' }), value: 'months' },
]

const activeBookingDropDownOptions: Array<{
  label: string
  value: ActiveBookingType
}> = [
  { label: ctIntl.formatMessage({ id: 'Geofence' }), value: 'geofence' },
  {
    label: ctIntl.formatMessage({ id: 'carpool.list.keyCollection' }),
    value: 'keyCollection',
  },
  {
    label: ctIntl.formatMessage({ id: 'Checklist' }),
    value: 'checklist',
  },
]

const durationRuleValidationSchema = z.object({
  value: z.coerce.number().int().min(1, messages.validInteger),
})

const Rules = () => {
  const searchProps = useSearchTextField('')
  const specialLicensesLabel = useTypedSelector(getSpecialLicensesDynamicFeatureName)

  const rulesQuery = useRules()
  const updateRuleMutation = useRuleMutation()
  const keyCollectionRule = useKeyCollectionRule()
  const canEditRules = useTypedSelector(getCarpoolEditRules)

  const [editingRule, setEditingRule] = useState<Rule | undefined>(undefined)

  const columnsGetters = useMemo(
    () => ({
      bookingRule: (rule: Rule) =>
        rule.translationKey
          ? ctIntl.formatMessage({
              id: mapRulesToTranslationKeys(
                rule.translationKey,
                specialLicensesLabel.includes('Police'),
              ),
            })
          : rule.bookingRule,
      status: (rule: Rule) => rule.status,
      value: (rule: Rule) => rule.value,
    }),
    [specialLicensesLabel],
  )

  const filteredRules = useMemo(() => {
    if (rulesQuery.data === undefined) {
      return []
    }
    const searchFilters: Filters<Rule> = {
      search: [columnsGetters.bookingRule],
    }
    const { itemMatchesWithTextAndFilters } = generateItemMatchesWithTextAndFilters(
      searchProps.value,
    )
    return rulesQuery.data.filter(
      (rule: Rule) =>
        !RULE_TYPES.ALERTS.includes(rule.id) &&
        itemMatchesWithTextAndFilters(rule, searchFilters),
    )
  }, [columnsGetters, searchProps.value, rulesQuery.data])

  const cancelRowEdition = () => setEditingRule(undefined)

  const columns = useMemo(
    (): Array<GridColDef<Rule>> => [
      {
        field: 'reason',
        headerName: ctIntl.formatMessage({ id: 'Reason' }),
        valueGetter: (_, row) => columnsGetters.bookingRule(row),
        flex: 1,
      },
      {
        field: 'value',
        headerName: ctIntl.formatMessage({ id: 'Value' }),
        valueGetter: (_, row) => columnsGetters.status(row),
        type: 'boolean',
        filterable: false,
        pinnable: false,
        sortable: false,
        renderCell: ({ row }) => {
          if (row.unit === 'bool') {
            // It is a On/Off row - nothing to show if so
            return null
          }

          if (row.id !== editingRule?.id) {
            const ruleValue = match(row.id)
              .with(P.union(...RULE_TYPES.DURATION), () => `${row.value} ${row.unit}`)
              .with(
                RULES.DISABLE_AUTO_APPROVE_ACTIVE_BOOKING,
                () =>
                  `${
                    activeBookingDropDownOptions.find((o) => o.value === row.value)
                      ?.label ?? ''
                  }`,
              )
              .otherwise(() => undefined)
            // It's a editable row but not in edit mode yet
            return (
              <>
                <Typography
                  sx={{
                    mb: '4px',
                    mt: '4px',
                    maxWidth: '80%',
                    whiteSpace: 'nowrap',
                    overflow: 'hidden',
                    textOverflow: 'ellipsis',
                  }}
                  title={ruleValue}
                >
                  {ruleValue || ctIntl.formatMessage({ id: 'No data available' })}
                </Typography>
                {canEditRules && (
                  <GridActionsCellItem
                    icon={<EditOutlinedIcon />}
                    label={ctIntl.formatMessage({ id: 'Edit' })}
                    onClick={() => setEditingRule(row)}
                    color="inherit"
                  />
                )}
              </>
            )
          }

          // Editable row in edit mode
          return (
            <>
              {match(row.id)
                .with(P.union(...RULE_TYPES.DURATION), () => (
                  <>
                    <TextField
                      sx={{ width: '100px' }}
                      id={`outlined-basic-${row.id}`}
                      label={ctIntl.formatMessage({
                        id: 'carpool.settings.rules.value.placeholder',
                      })}
                      value={editingRule.value}
                      onChange={(event) =>
                        setEditingRule({
                          ...editingRule,
                          value: event.currentTarget.value,
                        })
                      }
                    />
                    <Autocomplete
                      sx={{ ml: 1, minwidth: '150px' }}
                      options={unitDropDownOptions}
                      value={unitDropDownOptions.find(
                        (o) => o.value === editingRule.unit,
                      )}
                      defaultValue={unitDropDownOptions[0]}
                      disableClearable
                      onChange={(_, resultOption) =>
                        setEditingRule({
                          ...editingRule,
                          unit: resultOption?.value || unitDropDownOptions[0].value,
                        })
                      }
                      renderInput={(params) => (
                        <TextField
                          {...params}
                          label={ctIntl.formatMessage({ id: 'Unit' })}
                        />
                      )}
                    />
                  </>
                ))
                .with(RULES.DISABLE_AUTO_APPROVE_ACTIVE_BOOKING, () => {
                  if (!editingRule.value) {
                    setEditingRule({
                      ...editingRule,
                      value: activeBookingDropDownOptions[0].value,
                    })
                  }
                  return (
                    <Autocomplete
                      sx={{ minWidth: '200px' }}
                      options={activeBookingDropDownOptions}
                      value={activeBookingDropDownOptions.find(
                        (o) => o.value === editingRule.value,
                      )}
                      defaultValue={activeBookingDropDownOptions[0]}
                      disableClearable
                      getOptionDisabled={(option) =>
                        !keyCollectionRule && option.value === 'keyCollection'
                      }
                      onChange={(_, resultOption) =>
                        setEditingRule({
                          ...editingRule,
                          value:
                            resultOption?.value ||
                            activeBookingDropDownOptions[0].value,
                        })
                      }
                      renderInput={(params) => (
                        <TextField
                          {...params}
                          label={ctIntl.formatMessage({
                            id: 'carpool.settings.rules.value.placeholder',
                          })}
                        />
                      )}
                    />
                  )
                })
                .otherwise(() => null)}

              <GridActionsCellItem
                disabled={
                  RULE_TYPES.DURATION.includes(editingRule.id)
                    ? !durationRuleValidationSchema.safeParse(editingRule).success
                    : false
                }
                onClick={() => {
                  updateRuleMutation.mutate({
                    id: row.id,
                    status: row.status,
                    value: isEmpty(editingRule.value)
                      ? null
                      : (editingRule.value as string | null),
                    unit: editingRule.unit,
                  })
                  cancelRowEdition()
                }}
                icon={<CheckIcon sx={{ color: 'success.main' }} />}
                label={ctIntl.formatMessage({ id: 'Save' })}
                color="primary"
              />
              <GridActionsCellItem
                key="cancel"
                icon={<CloseIcon sx={{ color: 'error.main' }} />}
                label={ctIntl.formatMessage({ id: 'Cancel' })}
                onClick={cancelRowEdition}
                color="inherit"
              />
            </>
          )
        },
        flex: 1,
      },
      {
        field: 'status',
        headerName: ctIntl.formatMessage({ id: 'Status' }),
        valueGetter: (_, row) => columnsGetters.status(row),
        type: 'boolean',
        renderCell: ({ row }) => (
          <>
            <Switch
              defaultChecked={row.status}
              checked={row.status}
              disabled={!canEditRules}
              onChange={(e) => {
                cancelRowEdition()
                updateRuleMutation.mutate({
                  id: row.id,
                  status: e.target.checked,
                  value: row.value,
                  unit: row.unit,
                })
              }}
            />
            <Typography>
              {row.status
                ? ctIntl.formatMessage({ id: 'Active' })
                : ctIntl.formatMessage({ id: 'Inactive' })}
            </Typography>
          </>
        ),
        flex: 1,
      },
    ],
    [canEditRules, columnsGetters, editingRule, keyCollectionRule, updateRuleMutation],
  )

  return (
    <Box
      sx={{
        display: 'flex',
        flexFlow: 'column',
        height: '100%',
        width: '100%',
        gap: 2,
      }}
    >
      <UserDataGridWithSavedSettingsOnIDB
        Component={DataGrid}
        sx={{
          '& .MuiDataGrid-row': {
            cursor: 'pointer',
          },
        }}
        dataGridId="rules"
        disableVirtualization
        loading={rulesQuery.isPending || updateRuleMutation.isPending}
        autoPageSize
        slots={{
          toolbar: KarooToolbar,
          loadingOverlay: LinearProgress,
        }}
        slotProps={{
          toolbar: KarooToolbar.createProps({
            slots: {
              searchFilter: { show: true, props: { quickSearchDebounceMs: 500 } },
              settingsButton: { show: true },
              filterButton: { show: true },
            },
          }),
          basePagination: { material: { showFirstButton: true, showLastButton: true } },
        }}
        columns={columns}
        rows={filteredRules}
        pagination
        disableRowSelectionOnClick
      />
    </Box>
  )
}

export default Rules
