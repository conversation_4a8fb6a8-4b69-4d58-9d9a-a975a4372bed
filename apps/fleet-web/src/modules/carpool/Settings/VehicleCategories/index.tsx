import { use<PERSON><PERSON>back, useMemo, useState } from 'react'
import {
  Box,
  Button,
  createDataGridBaseColumn,
  DataGrid,
  GridActionsCellItem,
  GridRowModes,
  LinearProgress,
  Stack,
  TextField,
  useGridApiContext,
  useSearchTextField,
  type GridColDef,
  type GridRenderEditCellParams,
  type GridRowId,
  type GridRowModesModel,
  type GridRowSelectionModel,
} from '@karoo-ui/core'
import AddIcon from '@mui/icons-material/Add'
import CheckIcon from '@mui/icons-material/Check'
import CloseIcon from '@mui/icons-material/Close'
import DeleteOutlinedIcon from '@mui/icons-material/DeleteOutlined'
import EditOutlinedIcon from '@mui/icons-material/EditOutlined'
import { mapValues } from 'remeda'
import { match, P } from 'ts-pattern'
import type { ReadonlyDeep } from 'type-fest'
import { useEffectReducer, type EffectReducer } from 'use-effect-reducer'
import { z } from 'zod'

import {
  getCarpoolAddVehicleCategories,
  getCarpoolDeleteVehicleCategories,
  getCarpoolEditVehicleCategories,
} from 'duxs/user-sensitive-selectors'
import ArrowedTooltip from 'src/components/_popups/Tooltip/Arrowed'
import { useModal } from 'src/hooks'
import { UserDataGridWithSavedSettingsOnIDB } from 'src/modules/components/connected'
import { useTypedSelector } from 'src/redux-hooks'
import { DataGridDeleteButtonWithCounter } from 'src/shared/data-grid/DataGridDeleteButtonWithCounter'
import KarooToolbar from 'src/shared/data-grid/KarooToolbar'
import { messages } from 'src/shared/formik'
import { ctIntl } from 'src/util-components/ctIntl'
import {
  generateItemMatchesWithTextAndFilters,
  type Filters,
} from 'src/util-functions/search-utils'

import {
  useDeleteVehicleCategoriesMutation,
  useUpdateVehicleCategoryMutation,
  useVehicleCategories,
  type VehicleCategory,
} from './api/queries'
import DeleteVehicleCategoriesModal from './DeleteVehicleCategoriesModal'
import NewVehicleCategoryDrawer from './NewVehicleCategoryDrawer'

type VehicleCategoryCellValue = string | null
type CellExtraParams = {
  customParams?: {
    error: boolean
  }
}

const columnsFields = {
  vehicleType: 'vehicleType',
}

const editRowValidationSchema = z.object({
  vehicleType: z.string().min(1, { message: messages.required }),
})

type EditRowValidationSchema = z.infer<typeof editRowValidationSchema>

type ReducerState = ReadonlyDeep<{
  rowModesModel: GridRowModesModel
}>

type ReducerEvent =
  | {
      type: 'onRowSaveClick'
      id: GridRowId
    }
  | {
      type: 'onRowCancelClick'
      id: GridRowId
    }
  | {
      type: 'onRowEditClick'
      id: GridRowId
    }

type RowModel = VehicleCategory

export const createReducer =
  (): EffectReducer<ReducerState, ReducerEvent> => (state, event, _captureEffect) =>
    match([state, event])
      .with(
        [P.any, { type: 'onRowCancelClick' }],
        ([, { id }]): ReducerState => ({
          ...state,
          rowModesModel: {
            ...state.rowModesModel,
            [id]: { mode: GridRowModes.View, ignoreModifications: true },
          },
        }),
      )
      .with(
        [P.any, { type: 'onRowEditClick' }],
        ([, { id }]): ReducerState => ({
          ...state,
          rowModesModel: {
            ...mapValues(state.rowModesModel, (value, key) => {
              if (key === id) {
                return value
              }
              return { mode: GridRowModes.View, ignoreModifications: true }
            }),
            [id]: { mode: GridRowModes.Edit },
          },
        }),
      )
      .with(
        [P.any, { type: 'onRowSaveClick' }],
        ([, { id }]): ReducerState => ({
          ...state,
          rowModesModel: {
            ...state.rowModesModel,
            [id]: { mode: GridRowModes.View },
          },
        }),
      )
      .otherwise(() => state)

const VehicleCategories = () => {
  const searchProps = useSearchTextField('')
  const [newCategoryDrawerOpen, newCategoryDrawer] = useModal(false)
  const [isDeleteConfirmModalOpen, setIsDeleteConfirmModalOpen] = useState(false)
  const [selectedVehicleCategories, setSelectedVehicleCategories] = useState<
    ReadonlySet<GridRowId>
  >(new Set())
  const [deleteVehicleCategoryId, setDeleteVehicleCategoryId] =
    useState<GridRowId | null>(null)
  const vehicleCategoriesQuery = useVehicleCategories()
  const updateVehicleCategory = useUpdateVehicleCategoryMutation()
  const deleteVehicleCategoriesMutation = useDeleteVehicleCategoriesMutation()
  const addVehicleCategories = useTypedSelector(getCarpoolAddVehicleCategories)
  const editVehicleCategories = useTypedSelector(getCarpoolEditVehicleCategories)
  const deleteVehicleCategories = useTypedSelector(getCarpoolDeleteVehicleCategories)

  const isMutating =
    updateVehicleCategory.isPending || deleteVehicleCategoriesMutation.isPending

  const columnsGetters = useMemo(
    () => ({
      vehicleType: (c: VehicleCategory) => c.vehicleType,
    }),
    [],
  )

  const processRowUpdate = useCallback(
    (newRow: RowModel, oldRow: RowModel): Promise<RowModel> | RowModel =>
      new Promise((resolve, _reject) => {
        const rollbackChanges = () => resolve(oldRow)
        updateVehicleCategory.mutate(
          {
            id: newRow.id,
            vehicleType: newRow.vehicleType,
          },
          {
            onSuccess() {
              resolve(newRow)
            },
            onError() {
              rollbackChanges()
            },
          },
        )
      }),
    [updateVehicleCategory],
  )

  const filteredVehicleCategories = useMemo((): Array<RowModel> => {
    if (vehicleCategoriesQuery.data === undefined) {
      return []
    }
    const searchFilters: Filters<VehicleCategory> = {
      search: [columnsGetters.vehicleType],
    }
    const { itemMatchesWithTextAndFilters } = generateItemMatchesWithTextAndFilters(
      searchProps.value,
    )
    return vehicleCategoriesQuery.data.filter((category: VehicleCategory) =>
      itemMatchesWithTextAndFilters(category, searchFilters),
    )
  }, [columnsGetters, searchProps.value, vehicleCategoriesQuery.data])

  const [{ rowModesModel }, dispatch] = useEffectReducer(
    createReducer(),
    (): ReducerState => ({ rowModesModel: {} }),
  )

  const columns = useMemo(
    (): Array<GridColDef<VehicleCategory>> => [
      createDataGridBaseColumn<VehicleCategory, string>({
        field: columnsFields.vehicleType,
        headerName: ctIntl.formatMessage({ id: 'Category' }),
        valueGetter: (_, row) => columnsGetters.vehicleType(row),
        flex: 1,
        editable: true,
        renderEditCell: (params) => <VehicleCategoryEditCell {...params} />,
        preProcessEditCellProps: (params) => {
          if (params.otherFieldsProps === undefined) {
            return params.props
          }

          const object: EditRowValidationSchema = {
            vehicleType: params.props.value,
          }
          const parseResult = editRowValidationSchema.safeParse(object, {
            path: [columnsFields.vehicleType],
          })

          const extra: CellExtraParams = {
            customParams: {
              error: parseResult.success === false,
            },
          }
          return { ...params.props, ...extra }
        },
        valueSetter: (value, row) => ({
          ...row,
          vehicleType: value,
        }),
      }),
      {
        field: 'actions',
        type: 'actions',
        headerName: 'Actions',
        getActions: ({ id }) => {
          const isInEditMode = rowModesModel[id]?.mode === GridRowModes.Edit
          if (isInEditMode) {
            return [
              <GridActionSaveButton
                key="save"
                rowId={id}
                onClick={() => dispatch({ type: 'onRowSaveClick', id })}
              />,
              <GridActionsCellItem
                key="cancel"
                icon={<CloseIcon sx={{ color: 'error.main' }} />}
                label={ctIntl.formatMessage({ id: 'Cancel' })}
                onClick={() => dispatch({ type: 'onRowCancelClick', id })}
                color="inherit"
              />,
            ]
          }

          return [
            <ArrowedTooltip
              key="edit"
              label={ctIntl.formatMessage({ id: 'Edit' })}
            >
              <span>
                <GridActionsCellItem
                  disabled={isMutating || !editVehicleCategories}
                  icon={<EditOutlinedIcon />}
                  label={ctIntl.formatMessage({ id: 'Edit' })}
                  onClick={() => dispatch({ type: 'onRowEditClick', id })}
                />
              </span>
            </ArrowedTooltip>,
            <ArrowedTooltip
              key="delete"
              label={ctIntl.formatMessage({ id: 'Delete' })}
            >
              <span>
                <GridActionsCellItem
                  disabled={isMutating || !deleteVehicleCategories}
                  icon={<DeleteOutlinedIcon />}
                  label={ctIntl.formatMessage({ id: 'Delete' })}
                  onClick={() => {
                    setDeleteVehicleCategoryId(id)
                    setIsDeleteConfirmModalOpen(true)
                  }}
                />
              </span>
            </ArrowedTooltip>,
          ]
        },
      },
    ],
    [
      columnsGetters,
      rowModesModel,
      isMutating,
      editVehicleCategories,
      deleteVehicleCategories,
      dispatch,
    ],
  )

  const handleSelectionModelChange = (selectionModal: GridRowSelectionModel) => {
    setSelectedVehicleCategories(selectionModal.ids)
  }

  const handleDeleteVehicleCategories = () => {
    deleteVehicleCategoriesMutation.mutate(
      {
        ids: (deleteVehicleCategoryId
          ? [deleteVehicleCategoryId]
          : [...selectedVehicleCategories]) as Array<number>,
      },
      {
        onSuccess: () => {
          if (deleteVehicleCategoryId) {
            setDeleteVehicleCategoryId(null)
            setSelectedVehicleCategories(
              new Set(
                [...selectedVehicleCategories].filter(
                  (c) => c !== deleteVehicleCategoryId,
                ),
              ),
            )
          } else {
            setSelectedVehicleCategories(new Set())
          }
        },
      },
    )
  }

  return (
    <Box
      sx={{
        display: 'flex',
        flexFlow: 'column',
        height: '100%',
        width: '100%',
        gap: 2,
      }}
    >
      <UserDataGridWithSavedSettingsOnIDB
        dataGridId="vehicle-categories"
        Component={DataGrid}
        sx={{ '& .MuiDataGrid-row': { cursor: 'pointer' } }}
        checkboxSelection={deleteVehicleCategories}
        disableRowSelectionOnClick
        rowSelectionModel={selectedVehicleCategories}
        onRowSelectionModelChange={handleSelectionModelChange}
        editMode="row"
        disableClipboardPaste
        disableVirtualization
        autoPageSize
        loading={vehicleCategoriesQuery.isFetching || isMutating}
        slots={{
          toolbar: KarooToolbar,
          loadingOverlay: LinearProgress,
        }}
        slotProps={{
          toolbar: KarooToolbar.createProps({
            slots: {
              searchFilter: { show: true, props: { quickSearchDebounceMs: 500 } },
              settingsButton: { show: true },
              filterButton: { show: true },
            },
            extraContent: {
              right: (
                <Stack
                  direction="row"
                  gap={1}
                >
                  <Button
                    variant="contained"
                    onClick={() => {
                      newCategoryDrawer.open()
                    }}
                    size="small"
                    startIcon={<AddIcon />}
                    disabled={!addVehicleCategories}
                  >
                    {ctIntl.formatMessage({ id: 'Add New' })}
                  </Button>
                  <DataGridDeleteButtonWithCounter
                    disabled={!deleteVehicleCategories}
                    count={selectedVehicleCategories.size}
                    ButtonProps={{
                      onClick: () => setIsDeleteConfirmModalOpen(true),
                    }}
                  />
                </Stack>
              ),
            },
          }),
          basePagination: { material: { showFirstButton: true, showLastButton: true } },
        }}
        columns={columns}
        rows={filteredVehicleCategories}
        pagination
        processRowUpdate={processRowUpdate}
        onRowEditStart={(_, event) => {
          // eslint-disable-next-line no-param-reassign
          event.defaultMuiPrevented = true
        }}
        onRowEditStop={(_, event) => {
          // eslint-disable-next-line no-param-reassign
          event.defaultMuiPrevented = true
        }}
        onCellEditStart={(_, event) => {
          // eslint-disable-next-line no-param-reassign
          event.defaultMuiPrevented = true
        }}
        rowModesModel={rowModesModel}
      />

      {newCategoryDrawerOpen && (
        <NewVehicleCategoryDrawer onClose={() => newCategoryDrawer.close()} />
      )}

      {isDeleteConfirmModalOpen && (
        <DeleteVehicleCategoriesModal
          onClose={() => {
            setIsDeleteConfirmModalOpen(false)
            setDeleteVehicleCategoryId(null)
          }}
          onConfirm={() => {
            handleDeleteVehicleCategories()
            setIsDeleteConfirmModalOpen(false)
          }}
        />
      )}
    </Box>
  )
}

export default VehicleCategories

function VehicleCategoryEditCell({
  id,
  value,
  field,
  customParams,
}: GridRenderEditCellParams<VehicleCategory, VehicleCategoryCellValue> &
  CellExtraParams) {
  const apiRef = useGridApiContext()
  return (
    <TextField
      sx={{
        ml: 2,
        mr: 2,
      }}
      fullWidth
      error={customParams?.error}
      required
      value={value ?? null}
      onChange={(e) => {
        const newValue: VehicleCategoryCellValue = e.target.value
        apiRef.current.setEditCellValue({
          id,
          field,
          value: newValue,
        })
      }}
      onClick={(e) => {
        e.stopPropagation()
      }}
    />
  )
}

export function GridActionSaveButton({
  rowId,
  onClick,
}: {
  rowId: GridRowId
  onClick: () => void
}) {
  const apiRef = useGridApiContext()
  const editRowProps = apiRef.current.getRowWithUpdatedValues(rowId, '') as RowModel

  const object: EditRowValidationSchema = {
    vehicleType: editRowProps.vehicleType,
  }
  const isFormValid = editRowValidationSchema.safeParse(object).success

  return (
    <GridActionsCellItem
      disabled={!isFormValid}
      icon={<CheckIcon sx={{ color: 'success.main' }} />}
      label={ctIntl.formatMessage({ id: 'Save' })}
      color="primary"
      onClick={onClick}
    />
  )
}
