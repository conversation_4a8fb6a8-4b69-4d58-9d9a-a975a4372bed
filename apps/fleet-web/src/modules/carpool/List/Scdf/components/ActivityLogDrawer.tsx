import { useCallback } from 'react'
import {
  Box,
  CircularProgressDelayedCentered,
  IconButton,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  Stack,
  Typography,
} from '@karoo-ui/core'
import AccessTimeIcon from '@mui/icons-material/AccessTime'
import AssignmentLateOutlinedIcon from '@mui/icons-material/AssignmentLateOutlined'
import CheckCircleIcon from '@mui/icons-material/CheckCircle'
import CloseIcon from '@mui/icons-material/Close'
import DateRangeOutlinedIcon from '@mui/icons-material/DateRangeOutlined'
import DoDisturbAltIcon from '@mui/icons-material/DoDisturbAlt'
import DoneAllIcon from '@mui/icons-material/DoneAll'
import EditIcon from '@mui/icons-material/Edit'
import IndeterminateCheckBoxOutlinedIcon from '@mui/icons-material/IndeterminateCheckBoxOutlined'
import { match } from 'ts-pattern'

import type { FetchDriversResponse } from 'api/drivers'
import useDriversListQuery from 'api/drivers/useDriversListQuery'
import type { CarpoolBookingId, ClientUserId, DriverId, VehicleId } from 'api/types'
import { useUsersQuery, type UseUsersQueryData } from 'src/modules/api/useUsersQuery'
import DrawerBase from 'src/modules/components/unconnected/DrawerBase'

import CarpoolStatusChip from '../../../components/CarpoolStatusChip'
import { BookingStatus } from '../../../utils/constants'
import BookingVehicleMapTrip from '../../components/BookingVehicleMapTrip'
import {
  useActivityLogQuery,
  type ActivityLogResponse,
} from '../api/useActivityLogQuery'

type Props = {
  onClose: () => void
  bookingId: CarpoolBookingId
  bookingStatus: BookingStatus
  vehicleId: VehicleId
  startDate: string
  endDate: string
}

// Combined function to get both icon and text for activity
const getActivityIconAndText = ({
  activity,
  getDriverById,
  usersById,
}: {
  activity: ActivityLogResponse['activities'][number]
  getDriverById: (
    driverId: DriverId,
  ) => FetchDriversResponse['drivers'][number] | undefined
  usersById: UseUsersQueryData['usersById'] | undefined
}) => {
  const { action, auClientUser, timestamp } = activity
  const formatedTime = timestamp.toFormat('ff')
  const user = auClientUser.clientUserId
    ? usersById?.get(auClientUser.clientUserId as ClientUserId) ?? null
    : null

  const userName = user ? user.username : null

  const userText =
    userName === auClientUser.username || userName === null
      ? auClientUser.username
      : userName + ' ' + auClientUser.username

  return match(action)
    .with({ statusId: BookingStatus.BOOKING_STATUS_REQUESTED }, () => ({
      icon: <DateRangeOutlinedIcon />,
      primaryText: userText + ` created this booking request ${formatedTime}`,
      secondaryText: null,
    }))
    .with(
      { statusId: BookingStatus.BOOKING_STATUS_APPROVED },
      ({ assignmentDetails, formattedChanges }) => {
        const assignedDriver = getDriverById(assignmentDetails.assignedDriverId)
        const changesText = [
          assignedDriver ? 'Assigned Driver: ' + assignedDriver.name : null,
          assignmentDetails.assignedVehicleCommander
            ? `Assigned Vehicle Commander: ${assignmentDetails.assignedVehicleCommander}`
            : null,
          ...formattedChanges,
        ]
          .filter(Boolean)
          .join('\n')

        return {
          icon: <CheckCircleIcon />,
          primaryText: userText + ` approved this booking request ${formatedTime}`,
          secondaryText: changesText || null,
        }
      },
    )
    .with(
      { statusId: BookingStatus.BOOKING_STATUS_DECLINED },
      ({ reason, remarks }) => {
        const reasonText = `Reason: ${reason}`
        const remarksText = remarks ? `Remarks: ${remarks}` : null
        const secondaryText = [reasonText, remarksText].filter(Boolean).join('\n')

        return {
          icon: <DoDisturbAltIcon />,
          primaryText: userText + ` rejected this booking request ${formatedTime}`,
          secondaryText,
        }
      },
    )
    .with(
      { statusId: BookingStatus.BOOKING_STATUS_CANCELLED },
      ({ reason, remarks }) => {
        const reasonText = `Reason: ${reason}`
        const remarksText = remarks ? `Remarks: ${remarks}` : null
        const secondaryText = [reasonText, remarksText].filter(Boolean).join('\n')

        return {
          icon: <CloseIcon />,
          primaryText: userText + ` cancelled this booking request ${formatedTime}`,
          secondaryText,
        }
      },
    )
    .with(
      { statusId: BookingStatus.BOOKING_STATUS_ACTIVE },
      ({ formattedPickupTime, formattedChanges }) => {
        const changesText = [
          formattedPickupTime
            ? `Actual Pick-up date/time: ${formattedPickupTime}`
            : null,
          ...formattedChanges,
        ]
          .filter(Boolean)
          .join('\n')

        return {
          icon: <AccessTimeIcon />,
          primaryText: userText + ` activated this booking ${formatedTime}`,
          secondaryText: changesText,
        }
      },
    )
    .with(
      { statusId: BookingStatus.BOOKING_STATUS_ACTIVE_LATE },
      ({ checkingStatusTime, formattedChanges }) => {
        const changesText = [
          checkingStatusTime
            ? `The booking is marked as 'Active Late': ${checkingStatusTime}`
            : null,
          ...formattedChanges,
        ]
          .filter(Boolean)
          .join('\n')
        return {
          icon: <AssignmentLateOutlinedIcon />,
          primaryText: 'The vehicle is still in use until the scheduled drop-off time.',
          secondaryText: changesText,
        }
      },
    )
    .with(
      { statusId: BookingStatus.BOOKING_STATUS_RETURNED },
      ({ formattedDropoffTime, formattedChanges }) => {
        const changesText = [
          formattedDropoffTime
            ? `Actual Drop-off date/time: ${formattedDropoffTime}`
            : null,
          ...formattedChanges,
        ]
          .filter(Boolean)
          .join('\n')

        return {
          icon: <DoneAllIcon />,
          primaryText: userText + ` ended this booking ${formatedTime}`,
          secondaryText: changesText,
        }
      },
    )
    .with(
      { statusId: BookingStatus.BOOKING_STATUS_RETURNED_LATE },
      ({ formattedDropoffTime, scheduledDropoffTime, formattedChanges }) => {
        const changesText = [
          scheduledDropoffTime
            ? `Scheduled Drop-off date/time: ${scheduledDropoffTime}\n`
            : null,
          formattedDropoffTime
            ? `Actual Drop-off date/time: ${formattedDropoffTime}`
            : null,
          ...formattedChanges,
        ]
          .filter(Boolean)
          .join('\n')

        return {
          icon: <DoneAllIcon />,
          primaryText: userText + ` ended this booking ${formatedTime}`,
          secondaryText: changesText,
        }
      },
    )
    .with(
      { statusId: BookingStatus.BOOKING_STATUS_FORCE_TERMINATED },
      ({ reason, remarks }) => {
        const reasonText = `Reason: ${reason}`
        const remarksText = remarks ? `Remarks: ${remarks}` : null
        const secondaryText = [reasonText, remarksText].filter(Boolean).join('\n')

        return {
          icon: <IndeterminateCheckBoxOutlinedIcon />,
          primaryText: userText + ` forced terminate this booking ${formatedTime}`,
          secondaryText,
        }
      },
    )
    .with({ statusId: 'EDIT' }, ({ formattedChanges }) => {
      const changesText = formattedChanges?.join('\n')

      return {
        icon: <EditIcon />,
        primaryText: userText + ` edited this booking ${formatedTime}`,
        secondaryText: changesText || null,
      }
    })
    .otherwise(() => ({
      icon: <AccessTimeIcon />,
      primaryText: userText + ` performed an action`,
      secondaryText: null,
    }))
}

const ActivityLogItem = ({
  activity,
  isLast,
  getDriverById,
  usersById,
}: {
  activity: ActivityLogResponse['activities'][number]
  isLast: boolean
  getDriverById: (
    driverId: DriverId,
  ) => FetchDriversResponse['drivers'][number] | undefined
  usersById: UseUsersQueryData['usersById'] | undefined
}) => {
  const { icon, primaryText, secondaryText } = getActivityIconAndText({
    activity,
    getDriverById,
    usersById,
  })

  return (
    <ListItem sx={{ px: 0, py: 0, my: 1, gap: 1, alignItems: 'stretch' }}>
      <ListItemIcon
        sx={{
          minWidth: 40,
          minHeight: '100%',
          display: 'flex',
          flexDirection: 'column',
          alignItems: 'center',
        }}
      >
        <IconButton
          size="small"
          sx={{
            backgroundColor: 'grey.200',
            flexShrink: 0,
            '& .MuiSvgIcon-root': { fontSize: 'inherit', color: 'text.secondary' },
          }}
        >
          {icon}
        </IconButton>
        {!isLast && (
          <Box // timeline line
            sx={{
              flex: '1 1 auto',
              width: '1px',
              backgroundColor: 'grey.400',
              minHeight: '24px',
              mt: 1,
            }}
          />
        )}
      </ListItemIcon>
      <ListItemText
        sx={{ my: 0 }}
        primary={<Typography sx={{ mb: 0.5 }}>{primaryText}</Typography>}
        secondary={
          <Stack
            spacing={0.5}
            sx={{ pb: 2 }}
          >
            {secondaryText && (
              <Typography
                color="text.secondary"
                sx={{ whiteSpace: 'pre-line' }}
              >
                {secondaryText}
              </Typography>
            )}
          </Stack>
        }
      />
    </ListItem>
  )
}

const ActivityLogDrawer = ({
  onClose,
  bookingId,
  bookingStatus,
  vehicleId,
  startDate,
  endDate,
}: Props) => {
  const activityLogQuery = useActivityLogQuery(bookingId)
  const driversListQuery = useDriversListQuery()
  const usersQuery = useUsersQuery()

  const getDriverById = useCallback(
    (driverId: DriverId) =>
      driversListQuery.data?.drivers.find((d) => d.id === driverId),
    [driversListQuery.data],
  )

  const usersById = usersQuery.data?.usersById

  return (
    <DrawerBase
      open
      onClose={onClose}
      header={
        <Stack spacing={2}>
          <Stack
            direction="row"
            justifyContent="space-between"
            alignItems="center"
          >
            <Typography variant="h6">Activity Log</Typography>
            <IconButton
              onClick={onClose}
              size="small"
            >
              <CloseIcon />
            </IconButton>
          </Stack>

          <Stack
            direction="row"
            spacing={1}
            alignItems="center"
          >
            <Typography color="text.secondary">
              Booking number: <b>{bookingId}</b>
            </Typography>
            <CarpoolStatusChip bookingStatusId={bookingStatus} />
          </Stack>
        </Stack>
      }
      PaperProps={{ sx: { width: '500px' } }}
    >
      <Stack
        spacing={2}
        mt={2}
      >
        <BookingVehicleMapTrip
          vehicleId={vehicleId}
          startDate={startDate}
          endDate={endDate}
        />
        {match(activityLogQuery)
          .with({ status: 'pending' }, () => <CircularProgressDelayedCentered />)
          .with({ status: 'error' }, () => null)
          .with({ status: 'success' }, ({ data }) => (
            <List sx={{ py: 0 }}>
              {data.activities.map((activity, index) => (
                <ActivityLogItem
                  key={activity.timestamp.toMillis()}
                  getDriverById={getDriverById}
                  usersById={usersById}
                  activity={activity}
                  isLast={index === data.activities.length - 1}
                />
              ))}
            </List>
          ))
          .exhaustive()}
      </Stack>
    </DrawerBase>
  )
}

export default ActivityLogDrawer
