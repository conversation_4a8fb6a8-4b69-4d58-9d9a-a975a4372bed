import { useCallback, useMemo, useState, type ReactElement } from 'react'
import { isEmpty, isNil } from 'lodash'
import {
  Box,
  Button,
  ContainerWithTabsForDataGrid,
  DataGridAsTabItem,
  GRID_CHECKBOX_SELECTION_COL_DEF,
  GridActionsCellItem,
  GridLogicOperator,
  LinearProgress,
  MenuItem,
  MenuList,
  Popover,
  Tooltip,
  Typography,
  useDataGridColumnHelper,
  type GridColDef,
  type GridFilterModel,
  type GridRowId,
  type GridSortModel,
} from '@karoo-ui/core'
import AddIcon from '@mui/icons-material/Add'
import CloseIcon from '@mui/icons-material/Close'
import InfoOutlinedIcon from '@mui/icons-material/InfoOutlined'
import KeyIcon from '@mui/icons-material/Key'
import ThreeDotsIcon from '@mui/icons-material/MoreVert'
import { DateTime } from 'luxon'
import PopupState, { bindPopover, bindTrigger } from 'material-ui-popup-state'
import { useHistory, useRouteMatch } from 'react-router'
import { match } from 'ts-pattern'
import type { ValueOf } from 'type-fest'

import type { CarpoolBookingId, ClientUserId, DriverId, VehicleId } from 'api/types'
import { buildRouteQueryString } from 'api/utils'
import { getAuthenticatedUser, getSettings_UNSAFE } from 'duxs/user'
import {
  getCarpoolApproveBookings,
  getCarpoolCancelBooking,
  getCarpoolChangeBookingToActive,
  getCarpoolDeclineBookings,
  getCarpoolEditBookings,
  getCarpoolForceTerminateBooking,
} from 'duxs/user-sensitive-selectors'
import PageWithMainTableContainer from 'src/components/_containers/PageWithMainTable'
import { useDriversQuery } from 'src/modules/api/useDriversQuery'
import { DETAILS_PREFIX } from 'src/modules/app/components/routes/carpool'
import { UserDataGridWithSavedSettingsOnIDB } from 'src/modules/components/connected/UserDataGridWithSavedSettingsOnIDB'
import { useTypedSelector } from 'src/redux-hooks'
import KarooToolbar from 'src/shared/data-grid/KarooToolbar'
import {
  mapFilterItemToServerFilter_Date,
  mapFilterItemToServerFilter_String,
} from 'src/shared/data-grid/server-client/utils'
import type { ExcludeStrict } from 'src/types/utils'
import { Array_filterMap } from 'src/util-functions/performance-critical-utils'

import { anomonymizeIdPassport } from 'cartrack-utils'
import { ctIntl } from 'cartrack-ui-kit'
import CarpoolStatusChip from '../../components/CarpoolStatusChip'
import { issuanceRequestSearchParamsSchema } from '../../components/ScdfIssuanceRequestDrawer/schema'
import StatBar from '../../components/StatBar'
import StatusInfoDrawer from '../../components/StatusInfoDrawer'
import useCarpoolOptionsQuery, {
  type FetchCarpoolOptionsParsedData,
} from '../../queries/useCarpoolOptionsQuery'
import {
  useApproveActiveRule,
  useKeyCollectionRule,
  useRuleLoadingStatus,
} from '../../Settings/Rules/api/queries'
import { BookingStatus, CustomTabs } from '../../utils/constants'
import {
  CarpoolOptionsContext,
  customTabAndStatusMapping,
  getSCDFTabOptions,
  LIST_TABS,
} from '../constants'
import type { FormPossibleValues as KeyCollectionFormPossibleValues } from '../Spf/components/KeyCollectionModal'
import KeyReturnModal from '../Spf/components/KeyReturnModal'
import useServerPaginationIssuanceListQuery, {
  type Booking,
  type FetchServerPaginationIssuanceList,
} from './api/useServerPaginationIssuanceListQuery'
import ActivateBookingModal from './components/ActivateBookingModal'
import ActivityLogDrawer from './components/ActivityLogDrawer'
import CancelBookingModal from './components/CancelBookingModal'
import EndBookingModal from './components/EndBookingModal'
import ForceTerminateBookingModal from './components/ForceTerminateBookingModal'
import {
  columnsIds,
  fetchIssuanceListFilterModelSchema,
  filterableColumnIds,
  metricConfig,
  sortableColumnIds,
  type FetchIssuanceListFilterModelSchemaSelf,
  type FilterableColumnId,
  type SortableColumnId,
} from './constants'

const columnsGetters = {
  bookingNumber: (booking: IssuanceItem) => booking.id,
  vehicle: (booking: IssuanceItem) => booking.vehicleRegistration ?? '',
  journeyType: (booking: IssuanceItem) => booking.journeyType ?? '',
  driverId: (booking: IssuanceItem) => booking.driverId ?? '',
  driverEmail: (booking: IssuanceItem) => booking.driverEmail ?? '',
  vehicleType: (booking: IssuanceItem) => booking.vehicleType ?? '',
  vehicleCommanderEmail: (booking: IssuanceItem) => booking.vehicleCommanderEmail,
  purpose: (booking: IssuanceItem) => booking.purpose ?? '',
  requestorEmail: (booking: IssuanceItem) => booking.requestorEmail,
  requestDate: (booking: IssuanceItem) =>
    booking.requestDate ? new Date(booking.requestDate) : null,
  startDate: (booking: IssuanceItem) =>
    booking.startDate ? new Date(booking.startDate) : null,
  pickUpAt: (booking: IssuanceItem) =>
    booking.pickupIgnitionTime ? new Date(booking.pickupIgnitionTime) : null,
  pickupLocation: (booking: IssuanceItem) => booking.pickupLocation ?? '',
  endDate: (booking: IssuanceItem) =>
    booking.endDate ? new Date(booking.endDate) : null,
  returnedAt: (booking: IssuanceItem) => {
    if (
      [
        BookingStatus.BOOKING_STATUS_RETURNED,
        BookingStatus.BOOKING_STATUS_RETURNED_LATE,
        BookingStatus.BOOKING_STATUS_FORCE_TERMINATED,
      ].includes(booking.statusId)
    ) {
      return booking.returnedIgnitionTime
        ? new Date(booking.returnedIgnitionTime)
        : null
    } else {
      return null
    }
  },
  returnedLocation: (booking: IssuanceItem) => booking.dropoffLocation ?? '',
  statusId: (booking: IssuanceItem) => booking.statusId,
  status: (booking: IssuanceItem) => booking.status,
  approvedBy: (booking: IssuanceItem) => booking.approvedBy,
  declinedBy: (booking: IssuanceItem) => booking.declinedBy,
}

type IssuanceItem = Booking

type DataGridSortModel = FetchServerPaginationIssuanceList.ApiInput['sort']

type DataGridFilterModel = FetchIssuanceListFilterModelSchemaSelf

const ScdfList = () => {
  const { path } = useRouteMatch()
  const history = useHistory()
  const columnHelper = useDataGridColumnHelper<IssuanceItem>({ filterMode: 'client' })
  const { carpoolAppName, isAdmin } = useTypedSelector(getSettings_UNSAFE)

  const [currentModal, setCurrentModal] = useState<
    | {
        type: 'key_return'
        data: {
          bookingId: CarpoolBookingId
          initialValues: KeyCollectionFormPossibleValues
        }
      }
    | {
        type: 'force_terminate_booking' | 'cancel_booking'
        data: { bookingIds: Array<CarpoolBookingId> }
      }
    | {
        type: 'end_booking'
        data: {
          bookingId: CarpoolBookingId
          bookingStatus: BookingStatus
          initialValues: {
            dropOffTime: string
            vehicleId: VehicleId
            driverId: DriverId
            vehicleCommanderId: ClientUserId | null
            vehicleTypeId: number | null
            locationId: number | null
            purposeid: number | null
            locationType: number
          }
        }
      }
    | {
        type: 'activate_booking'
        data: {
          bookingId: CarpoolBookingId
          initialValues: {
            pickUpTime: string
            vehicleId: VehicleId
            driverId: DriverId
            vehicleCommanderId: ClientUserId | null
            vehicleTypeId: number | null
            locationId: number | null
            purposeid: number | null
            locationType: number
          }
        }
      }
    | null
  >(null)

  const [currentDrawer, setCurrentDrawer] = useState<
    | {
        type: 'activity_log'
        data: {
          bookingId: CarpoolBookingId
          bookingStatus: BookingStatus
          vehicleId: VehicleId
          startDate: string
          endDate: string
        }
      }
    | { type: 'status_info' }
    | null
  >(null)

  const [tabs, setTabs] = useState<
    ReadonlyArray<{
      label: string
      value: ValueOf<typeof LIST_TABS>
      icon?: ReactElement
      iconPosition?: 'bottom' | 'top' | 'end' | 'start' | undefined
    }>
  >(getSCDFTabOptions(carpoolAppName))
  const [currentTab, setCurrentTab] = useState(
    getSCDFTabOptions(carpoolAppName)[0].value,
  )
  const [customSelection, setCustomSelection] = useState<CustomTabs | ''>('')
  const [multiSelectedBookingIds, setMultiSelectedBookingIds] = useState<
    ReadonlySet<CarpoolBookingId>
  >(new Set())

  // server side pagination, sorting, filtering
  const [paginationModel, setPaginationModel] = useState({ page: 0, pageSize: 25 })
  const [sortModel, setSortModel] = useState<DataGridSortModel>({
    field: columnsIds.startDate,
    sort: 'desc',
  })
  const [filterModel, setFilterModel] = useState<DataGridFilterModel>(() => ({
    items: [],
    logicOperator: GridLogicOperator.Or,
    quickFilterValues: [],
  }))

  const carpoolApproveBookings = useTypedSelector(getCarpoolApproveBookings)
  const carpoolDeclineBookings = useTypedSelector(getCarpoolDeclineBookings)
  const carpoolChangeBookingToActive = useTypedSelector(getCarpoolChangeBookingToActive)
  const carpoolCancelBooking = useTypedSelector(getCarpoolCancelBooking)
  const carpoolEditBookings = useTypedSelector(getCarpoolEditBookings)
  const carpoolForceTerminateBooking = useTypedSelector(getCarpoolForceTerminateBooking)

  const { cuid: clientUserId } = useTypedSelector(getAuthenticatedUser)

  const queryParam = useMemo(() => {
    const startDateFilter = filterModel.items.find(
      (item) => item.field === columnsIds.startDate,
    )

    const filteredItems = Array_filterMap(
      filterModel.items,
      (item, { RemoveSymbol }) => {
        switch (item.field) {
          case columnsIds.vehicle: {
            const { value, operator } = item
            const serverFilter = mapFilterItemToServerFilter_String({
              field: columnsIds.vehicle,
              value,
              operator,
              case: 'insensitive',
            })

            return serverFilter === 'TO_IGNORE' ? RemoveSymbol : serverFilter
          }
          case columnsIds.driver: {
            const { value, operator } = item
            const serverFilter = mapFilterItemToServerFilter_String({
              field: columnsIds.driver,
              value,
              operator,
              case: 'insensitive',
            })

            return serverFilter === 'TO_IGNORE' ? RemoveSymbol : serverFilter
          }
          case columnsIds.driverEmail: {
            const { value, operator } = item
            const serverFilter = mapFilterItemToServerFilter_String({
              field: columnsIds.driverEmail,
              value,
              operator,
              case: 'insensitive',
            })

            return serverFilter === 'TO_IGNORE' ? RemoveSymbol : serverFilter
          }
          case columnsIds.vehicleType: {
            const { value, operator } = item
            const serverFilter = mapFilterItemToServerFilter_String({
              field: columnsIds.vehicleType,
              value,
              operator,
              case: 'insensitive',
            })

            return serverFilter === 'TO_IGNORE' ? RemoveSymbol : serverFilter
          }
          case columnsIds.purpose: {
            const { value, operator } = item
            const serverFilter = mapFilterItemToServerFilter_String({
              field: columnsIds.purpose,
              value,
              operator,
              case: 'insensitive',
            })

            return serverFilter === 'TO_IGNORE' ? RemoveSymbol : serverFilter
          }
          case columnsIds.vehicleCommanderEmail: {
            const { value, operator } = item
            const serverFilter = mapFilterItemToServerFilter_String({
              field: columnsIds.vehicleCommanderEmail,
              value,
              operator,
              case: 'insensitive',
            })

            return serverFilter === 'TO_IGNORE' ? RemoveSymbol : serverFilter
          }
          case columnsIds.requestorEmail: {
            const { value, operator } = item
            const serverFilter = mapFilterItemToServerFilter_String({
              field: columnsIds.requestorEmail,
              value,
              operator,
              case: 'insensitive',
            })

            return serverFilter === 'TO_IGNORE' ? RemoveSymbol : serverFilter
          }
          case columnsIds.requestDate: {
            const { value, operator } = item
            const serverFilter = mapFilterItemToServerFilter_Date({
              field: columnsIds.requestDate,
              value,
              operator,
              precision: 'day',
            })
            return serverFilter === 'TO_IGNORE' ? RemoveSymbol : serverFilter
          }
          case columnsIds.startDate: {
            const { value, operator } = item
            const serverFilter = mapFilterItemToServerFilter_Date({
              field: columnsIds.startDate,
              value,
              operator,
              precision: 'day',
            })
            return serverFilter === 'TO_IGNORE' ? RemoveSymbol : serverFilter
          }
        }
      },
    )

    // if the current tab is TODAY, we need to add a filter for today's bookings
    if (currentTab === LIST_TABS.TODAY && !startDateFilter) {
      const todayRangeItem = mapFilterItemToServerFilter_Date({
        field: columnsIds.startDate,
        value: [DateTime.now(), DateTime.now()],
        operator: 'range',
        precision: 'day',
      })

      if (todayRangeItem !== 'TO_IGNORE') {
        filteredItems.push(todayRangeItem)
      }
    }

    return {
      pagination: {
        offset: paginationModel.page * paginationModel.pageSize,
        pageSize: paginationModel.pageSize,
      },
      sort: sortModel, // TODO: modify to multiple sort models
      filter: {
        searchTextFilterValues: filterModel.quickFilterValues,
        logicOperator: filterModel.logicOperator,
        items: filteredItems,
      },
      statusIds: match(currentTab)
        .with(LIST_TABS.SCHEDULED, () =>
          [
            BookingStatus.BOOKING_STATUS_APPROVED,
            BookingStatus.BOOKING_STATUS_REQUESTED,
          ].join(','),
        )
        .with(LIST_TABS.IN_PROGRESS, () =>
          [
            BookingStatus.BOOKING_STATUS_ACTIVE,
            BookingStatus.BOOKING_STATUS_ACTIVE_LATE,
          ].join(','),
        )
        .with(LIST_TABS.TODAY, () =>
          [
            BookingStatus.BOOKING_STATUS_REQUESTED,
            BookingStatus.BOOKING_STATUS_APPROVED,
            BookingStatus.BOOKING_STATUS_ACTIVE,
            BookingStatus.BOOKING_STATUS_ACTIVE_LATE,
            BookingStatus.BOOKING_STATUS_DECLINED,
            BookingStatus.BOOKING_STATUS_RETURNED,
            BookingStatus.BOOKING_STATUS_RETURNED_LATE,
            BookingStatus.BOOKING_STATUS_CANCELLED,
            BookingStatus.BOOKING_STATUS_FORCE_TERMINATED,
          ].join(','),
        )
        .with(LIST_TABS.HISTORY, () =>
          [
            BookingStatus.BOOKING_STATUS_CANCELLED,
            BookingStatus.BOOKING_STATUS_DECLINED,
            BookingStatus.BOOKING_STATUS_RETURNED,
            BookingStatus.BOOKING_STATUS_RETURNED_LATE,
            BookingStatus.BOOKING_STATUS_FORCE_TERMINATED,
          ].join(','),
        )
        .with(LIST_TABS.CUSTOM, () =>
          customSelection ? customTabAndStatusMapping[customSelection] : null,
        )
        .otherwise(() => null),
    } satisfies Parameters<typeof useServerPaginationIssuanceListQuery>[0]
  }, [
    currentTab,
    customSelection,
    filterModel.items,
    filterModel.logicOperator,
    filterModel.quickFilterValues,
    paginationModel.page,
    paginationModel.pageSize,
    sortModel,
  ])

  const issuanceListQuery = useServerPaginationIssuanceListQuery(queryParam)

  const driverQuery = useDriversQuery()
  const bookingOptionQuery = useCarpoolOptionsQuery()
  const keyCollectionRule = useKeyCollectionRule()
  const activeBookingRule = useApproveActiveRule()
  const isRuleFetching = useRuleLoadingStatus()

  const canApproveBooking = useCallback(
    (pendingManagers: Array<Array<string>>) =>
      isAdmin ||
      isEmpty(pendingManagers) ||
      pendingManagers[0].includes(clientUserId ?? ''),
    [clientUserId, isAdmin],
  )

  const renderForceTerminateAction = useCallback(
    (bookingId: CarpoolBookingId) =>
      carpoolForceTerminateBooking ? (
        <GridActionsCellItem
          label={ctIntl.formatMessage({ id: 'tfms.list.forceTerminate' })}
          onClick={() => {
            setCurrentModal({
              type: 'force_terminate_booking',
              data: { bookingIds: [bookingId] },
            })
          }}
          showInMenu
        />
      ) : (
        <></>
      ),
    [carpoolForceTerminateBooking],
  )

  const commonIssuanceActions = useCallback(
    ({
      bookingId,
      bookingStatus,
      vehicleId,
      startDate,
      endDate,
      showCancelAction = true,
      isInViewMode = false,
    }: {
      bookingId: CarpoolBookingId
      bookingStatus: BookingStatus
      vehicleId: VehicleId
      startDate: string
      endDate: string
      showCancelAction?: boolean
      isInViewMode?: boolean
    }) => {
      const mode =
        (bookingStatus !== BookingStatus.BOOKING_STATUS_REQUESTED &&
          !carpoolEditBookings) ||
        isInViewMode
          ? 'view'
          : 'edit'

      return [
        // rule to show the edit booking button:
        // 1. if the booking is requested, it should always show
        // 2. if the booking has other status, show it when carpoolEditBookings is true
        // 3. if isInViewMode is true, show it as view item always
        // otherwise, show the view booking item
        <GridActionsCellItem
          key={mode}
          label={ctIntl.formatMessage({
            id: mode === 'view' ? 'tfms.list.viewBooking' : 'tfms.list.editBooking',
          })}
          onClick={() =>
            history.push(
              `${path}/${DETAILS_PREFIX}?${buildRouteQueryString({
                schema: issuanceRequestSearchParamsSchema,
                searchParams: { type: mode, id: bookingId },
              })}`,
            )
          }
          showInMenu
        />,
        <GridActionsCellItem
          key="duplicate"
          label={ctIntl.formatMessage({ id: 'Duplicate' })}
          onClick={() =>
            history.push(
              `${path}/${DETAILS_PREFIX}?${buildRouteQueryString({
                schema: issuanceRequestSearchParamsSchema,
                searchParams: { type: 'duplicate', id: bookingId },
              })}`,
            )
          }
          showInMenu
        />,
        <GridActionsCellItem
          key="activityLog"
          label="Activity Log"
          onClick={() => {
            setCurrentDrawer({
              type: 'activity_log',
              data: { bookingId, bookingStatus, vehicleId, startDate, endDate },
            })
          }}
          showInMenu
        />,
        showCancelAction && carpoolCancelBooking ? (
          <GridActionsCellItem
            key="cancel"
            label={ctIntl.formatMessage({ id: 'carpool.list.cancelBooking' })}
            onClick={() =>
              setCurrentModal({
                type: 'cancel_booking',
                data: { bookingIds: [bookingId] },
              })
            }
            showInMenu
          />
        ) : (
          <></>
        ),
      ]
    },
    [carpoolCancelBooking, carpoolEditBookings, history, path],
  )

  const generateIssuanceActions = useCallback(
    (row: IssuanceItem) => {
      const {
        statusId,
        id: bookingId,
        driverId,
        vehicleId,
        vehicleTypeId,
        startDate,
        endDate,
        keyCollectionDate,
        keyReturnDate,
        pickupIgnitionTime,
        returnedIgnitionTime,
      } = row

      const initialValuesForKeyCollection: KeyCollectionFormPossibleValues = {
        driverId: driverId,
        vehicleId: vehicleId,
        vehicleType: vehicleTypeId,
      }

      const actualStartAndEndTime = match(statusId)
        .with(
          BookingStatus.BOOKING_STATUS_ACTIVE,
          BookingStatus.BOOKING_STATUS_ACTIVE_LATE,
          () => ({
            startDate: pickupIgnitionTime || startDate,
            endDate: DateTime.now().toString(),
          }),
        )
        .with(
          BookingStatus.BOOKING_STATUS_RETURNED,
          BookingStatus.BOOKING_STATUS_RETURNED_LATE,
          () => ({
            startDate: pickupIgnitionTime || startDate,
            endDate: returnedIgnitionTime || endDate,
          }),
        )
        .otherwise(() => ({
          startDate: pickupIgnitionTime || startDate,
          endDate,
        }))

      const vehicleTimelineParams = {
        // here vehicle id should not be null
        vehicleId: vehicleId as VehicleId,
        ...actualStartAndEndTime,
      }

      return match(statusId)
        .with(BookingStatus.BOOKING_STATUS_REQUESTED, () => [
          // TODO: need to consider the pendingManagers
          // !canApproveBooking(row.pendingManagers)
          carpoolApproveBookings || carpoolDeclineBookings ? (
            <GridActionsCellItem
              key="approve"
              label={ctIntl.formatMessage({
                id: match([carpoolApproveBookings, carpoolDeclineBookings])
                  .with([true, true], () => 'Approve/Reject')
                  .with([true, false], () => 'Approve')
                  .with([false, true], () => 'Decline')
                  .otherwise(() => ''),
              })}
              onClick={() =>
                history.push(
                  `${path}/${DETAILS_PREFIX}?${buildRouteQueryString({
                    schema: issuanceRequestSearchParamsSchema,
                    searchParams: { type: 'approve', id: bookingId },
                  })}`,
                )
              }
              showInMenu
            />
          ) : (
            <></>
          ),
          ...commonIssuanceActions({
            bookingId,
            bookingStatus: statusId,
            ...vehicleTimelineParams,
          }),
        ])
        .with(BookingStatus.BOOKING_STATUS_APPROVED, () => [
          carpoolChangeBookingToActive ? (
            <GridActionsCellItem
              key="changeToActive"
              label={ctIntl.formatMessage({ id: 'carpool.list.changeToActive' })}
              onClick={() =>
                setCurrentModal({
                  type: 'activate_booking',
                  data: {
                    bookingId,
                    initialValues: {
                      pickUpTime: row.startDate,
                      // here vehicle id and driver id should exist
                      vehicleId: row.vehicleId as VehicleId,
                      driverId: row.driverId as DriverId,
                      vehicleCommanderId: row.vehicleCommanderId as ClientUserId | null,
                      vehicleTypeId: row.vehicleTypeId,
                      locationId: row.pickupLocationId,
                      purposeid: row.purposeId,
                      locationType: row.locationType,
                    },
                  },
                })
              }
              showInMenu
            />
          ) : (
            <></>
          ),
          ...commonIssuanceActions({
            bookingId,
            bookingStatus: statusId,
            ...vehicleTimelineParams,
          }),
        ])
        .with(
          BookingStatus.BOOKING_STATUS_ACTIVE,
          BookingStatus.BOOKING_STATUS_ACTIVE_LATE,
          () => {
            const additionalAction = (() => {
              if (isRuleFetching) {
                return <></>
              }
              return match(activeBookingRule)
                .with('checklist', 'geofence', () =>
                  renderForceTerminateAction(bookingId),
                )
                .with('keyCollection', () => (
                  <>
                    {renderForceTerminateAction(bookingId)}
                    {keyCollectionRule ? (
                      <Tooltip
                        title={ctIntl.formatMessage({ id: 'carpool.list.returnKey' })}
                        arrow
                        key="returnKey"
                      >
                        <span>
                          <GridActionsCellItem
                            icon={<KeyIcon />}
                            label=""
                            onClick={() => {
                              setCurrentModal({
                                type: 'key_return',
                                data: {
                                  bookingId,
                                  initialValues: initialValuesForKeyCollection,
                                },
                              })
                            }}
                          />
                        </span>
                      </Tooltip>
                    ) : (
                      <></>
                    )}
                  </>
                ))
                .with('manual', () => renderForceTerminateAction(bookingId))
                .exhaustive()
            })()
            return [
              <GridActionsCellItem
                key="endBooking"
                label={ctIntl.formatMessage({ id: 'tfms.list.endBooking.title' })}
                onClick={() =>
                  setCurrentModal({
                    type: 'end_booking',
                    data: {
                      bookingId: bookingId,
                      bookingStatus: statusId,
                      initialValues: {
                        dropOffTime: row.endDate,
                        // here vehicle id and driver id should exist
                        vehicleId: row.vehicleId as VehicleId,
                        driverId: row.driverId as DriverId,
                        vehicleCommanderId:
                          row.vehicleCommanderId as ClientUserId | null,
                        vehicleTypeId: row.vehicleTypeId,
                        locationId: row.pickupLocationId,
                        purposeid: row.purposeId,
                        locationType: row.locationType,
                      },
                    },
                  })
                }
                showInMenu
              />,
              additionalAction,
              ...commonIssuanceActions({
                bookingId,
                bookingStatus: statusId,
                ...vehicleTimelineParams,
                showCancelAction: false,
              }),
            ]
          },
        )
        .with(
          BookingStatus.BOOKING_STATUS_CANCELLED,
          BookingStatus.BOOKING_STATUS_DECLINED,
          () =>
            commonIssuanceActions({
              bookingId,
              bookingStatus: statusId,
              ...vehicleTimelineParams,
              showCancelAction: false,
              isInViewMode: true,
            }),
        )
        .with(
          BookingStatus.BOOKING_STATUS_RETURNED,
          BookingStatus.BOOKING_STATUS_RETURNED_LATE,
          BookingStatus.BOOKING_STATUS_FORCE_TERMINATED,
          () => [
            keyCollectionRule ? (
              <Tooltip
                title={ctIntl.formatMessage({
                  id: isNil(keyReturnDate)
                    ? 'carpool.list.returnKey'
                    : 'carpool.list.keyIsReturned',
                })}
                key="returnKey"
              >
                <span>
                  <GridActionsCellItem
                    icon={<KeyIcon />}
                    label=""
                    onClick={() => {
                      setCurrentModal({
                        type: 'key_return',
                        data: {
                          bookingId,
                          initialValues: initialValuesForKeyCollection,
                        },
                      })
                    }}
                    disabled={!isNil(keyReturnDate) || isNil(keyCollectionDate)}
                  />
                </span>
              </Tooltip>
            ) : (
              <></>
            ),
            ...commonIssuanceActions({
              bookingId,
              bookingStatus: statusId,
              ...vehicleTimelineParams,
              showCancelAction: false,
            }),
          ],
        )
        .otherwise(() => [])
    },
    [
      activeBookingRule,
      carpoolApproveBookings,
      carpoolChangeBookingToActive,
      carpoolDeclineBookings,
      commonIssuanceActions,
      history,
      isRuleFetching,
      keyCollectionRule,
      path,
      renderForceTerminateAction,
    ],
  )

  const columns = useMemo((): Array<GridColDef<IssuanceItem>> => {
    type DriverMap = ExcludeStrict<
      (typeof driverQuery)['data'],
      undefined
    >['allDriversById']
    const allDriversById = driverQuery.data?.allDriversById ?? (new Map() as DriverMap)

    const base: Array<GridColDef<IssuanceItem>> = [
      columnHelper.string((_, row) => columnsGetters.bookingNumber(row), {
        field: columnsIds.bookingNumber,
        headerName: ctIntl.formatMessage({ id: 'carpool.list.bookingNumber' }),
        align: 'left',
      }),
      columnHelper.valueGetter((_, row) => columnsGetters.status(row), {
        field: columnsIds.status,
        headerName: ctIntl.formatMessage({ id: 'Status' }),
        renderCell: ({ row }) => (
          <CarpoolStatusChip
            bookingStatusId={columnsGetters.statusId(row) as BookingStatus}
          />
        ),
      }),
      columnHelper.string((_, row) => columnsGetters.journeyType(row), {
        field: columnsIds.journeyType,
        headerName: ctIntl.formatMessage({ id: 'carpool.journeyType' }),
      }),
      columnHelper.dateTime({
        field: columnsIds.startDate,
        headerName: ctIntl.formatMessage({ id: 'Pick Up At' }),
        valueGetter: (_, row) => columnsGetters.startDate(row),
      }),
      columnHelper.string((_, row) => columnsGetters.vehicle(row), {
        field: columnsIds.vehicle,
        headerName: ctIntl.formatMessage({ id: 'Vehicle' }),
      }),
      columnHelper.string(
        (_, row) => {
          const driverId = columnsGetters.driverId(row)
          const driver = allDriversById.get(driverId as DriverId)
          if (!driverId || !driver) {
            return ''
          }

          const idNumber = driver.idPassportNumber

          return `${driver.name}${
            idNumber ? ' (' + anomonymizeIdPassport(idNumber) + ')' : ''
          }`
        },
        {
          field: columnsIds.driver,
          headerName: ctIntl.formatMessage({ id: 'Driver' }),
        },
      ),
      columnHelper.string((_, row) => columnsGetters.driverEmail(row), {
        field: columnsIds.driverEmail,
        headerName: ctIntl.formatMessage({ id: 'Driver Email' }),
      }),
      columnHelper.string((_, row) => columnsGetters.vehicleCommanderEmail(row), {
        field: columnsIds.vehicleCommanderEmail,
        headerName: ctIntl.formatMessage({ id: 'carpool.vehicleCommander' }),
      }),
      columnHelper.string((_, row) => columnsGetters.purpose(row), {
        field: columnsIds.purpose,
        headerName: ctIntl.formatMessage({ id: 'carpool.list.purpose' }),
      }),
      columnHelper.string((_, row) => columnsGetters.requestorEmail(row), {
        field: columnsIds.requestorEmail,
        headerName: ctIntl.formatMessage({ id: 'scdf.list.requestor' }),
      }),
      columnHelper.dateTime({
        field: columnsIds.requestDate,
        headerName: ctIntl.formatMessage({ id: 'Request Date' }),
        valueGetter: (_, row) => columnsGetters.requestDate(row),
        filterOperators: columnHelper.utils
          .getGridDateColumnOperators({ showTime: false })
          .filter((operator) => operator.value === 'range'),
      }),
      columnHelper.string((_, row) => columnsGetters.vehicleType(row), {
        field: columnsIds.vehicleType,
        headerName: ctIntl.formatMessage({ id: 'carpool.vehicleCategory' }),
      }),
      columnHelper.dateTime({
        field: columnsIds.pickUpAt,
        headerName: ctIntl.formatMessage({ id: 'Actual Pick Up At' }),
        valueGetter: (_, row) => columnsGetters.pickUpAt(row),
      }),
      columnHelper.string((_, row) => columnsGetters.pickupLocation(row), {
        field: columnsIds.pickupLocation,
        headerName: ctIntl.formatMessage({ id: 'Pick-Up from' }),
      }),
      columnHelper.dateTime({
        field: columnsIds.endDate,
        headerName: ctIntl.formatMessage({ id: 'Drop-off At' }),
        valueGetter: (_, row) => columnsGetters.endDate(row),
      }),
      columnHelper.dateTime({
        field: columnsIds.returnedAt,
        headerName: ctIntl.formatMessage({ id: 'Actual Drop-off At' }),
        valueGetter: (_, row) => columnsGetters.returnedAt(row),
      }),
      columnHelper.string((_, row) => columnsGetters.returnedLocation(row), {
        field: columnsIds.returnedLocation,
        headerName: ctIntl.formatMessage({ id: 'Drop-off To' }),
      }),

      columnHelper.string((_, row) => columnsGetters.approvedBy(row), {
        field: columnsIds.approvedBy,
        headerName: ctIntl.formatMessage({ id: 'Approved By' }),
      }),
      columnHelper.string((_, row) => columnsGetters.declinedBy(row), {
        field: columnsIds.declinedBy,
        headerName: ctIntl.formatMessage({ id: 'Rejected By' }),
      }),
      {
        field: columnsIds.actions,
        type: 'actions',
        headerName: ctIntl.formatMessage({ id: 'Actions' }),
        align: 'right',
        getActions: ({ row }) => generateIssuanceActions(row),
      },
    ]

    return base.map((column) => ({
      ...column,
      sortable: sortableColumnIds.includes(column.field as SortableColumnId),
      filterable: filterableColumnIds.includes(column.field as FilterableColumnId),
    }))
  }, [columnHelper, driverQuery.data?.allDriversById, generateIssuanceActions])

  const onFilterModelChange = async (newFilterModel: GridFilterModel) => {
    setFilterModel(newFilterModel as DataGridFilterModel)

    // We set the filter model anyways but we try to throw an error while in dev for developers to fix it
    if (ENV.NODE_ENV === 'development') {
      // Might throw an error if the filodel is invalid (which is what we want)
      fetchIssuanceListFilterModelSchema.self.parse(newFilterModel)
      return
    }
    return
  }

  const handleCloseCustomTab = useCallback(() => {
    setCurrentTab(tabs[0].value)
    setTabs((prev) => {
      const newTabs = [...prev]
      newTabs.pop()

      return newTabs
    })
    setCustomSelection('')
  }, [tabs])

  const handleCustomSelect = useCallback(
    (target: CustomTabs, statusLabel: string) => {
      setTabs((prev) => [
        ...prev,
        {
          label: `${ctIntl.formatMessage({ id: 'Custom' })} - ${statusLabel}`,
          value: 'custom',
          icon: (
            <CloseIcon
              onClick={handleCloseCustomTab}
              fontSize="small"
            />
          ),
          iconPosition: 'end',
        },
      ])

      setCurrentTab('custom')
      setCustomSelection(target)
    },
    [handleCloseCustomTab],
  )

  const issuanceList = issuanceListQuery.data?.bookings ?? []

  const getBulkActionOptions = () => {
    if (isEmpty(multiSelectedBookingIds)) {
      return []
    }

    const allStatus = issuanceList
      .filter((booking) => multiSelectedBookingIds.has(booking.id))
      .map((booking) => columnsGetters.statusId(booking))

    if (!isEmpty(allStatus)) {
      if (
        allStatus.every((status) =>
          [
            BookingStatus.BOOKING_STATUS_ACTIVE,
            BookingStatus.BOOKING_STATUS_ACTIVE_LATE,
          ].includes(status),
        )
      ) {
        return [
          carpoolForceTerminateBooking ? (
            <MenuItem
              key="force_terminate_booking"
              onClick={() =>
                setCurrentModal({
                  type: 'force_terminate_booking',
                  data: { bookingIds: [...multiSelectedBookingIds] },
                })
              }
            >
              {ctIntl.formatMessage({ id: 'tfms.list.forceTerminate' })}
            </MenuItem>
          ) : (
            <></>
          ),
        ]
      }

      if (
        allStatus.every(
          (s) => BookingStatus.BOOKING_STATUS_REQUESTED === (s as BookingStatus),
        )
      ) {
        const canApproveBookings = issuanceList
          .filter((booking) => multiSelectedBookingIds.has(booking.id))
          .every((booking) => canApproveBooking(booking.pendingManagers))
        return [
          <MenuItem
            key="cancel_booking"
            onClick={() =>
              setCurrentModal({
                type: 'cancel_booking',
                data: {
                  bookingIds: [...multiSelectedBookingIds],
                },
              })
            }
            disabled={!carpoolApproveBookings || !canApproveBookings}
          >
            {ctIntl.formatMessage({ id: 'carpool.list.cancelBooking' })}
          </MenuItem>,
        ]
      }
    }

    return []
  }

  const handleSelectionModelChange = (selectionModel: {
    ids: ReadonlySet<GridRowId>
  }) => {
    setMultiSelectedBookingIds(
      new Set([...selectionModel.ids].map((id) => id.toString() as CarpoolBookingId)),
    )
  }

  const resetTablePagination = () => {
    setPaginationModel({ ...paginationModel, page: 0 })
  }

  const onSortModelChange = (_newSortModel: GridSortModel) => {
    // TODO: handle multiple sort models
    const newSortModel = _newSortModel[0] as DataGridSortModel

    resetTablePagination()
    setSortModel(newSortModel)
  }

  const listMetrics = useMemo(() => {
    if (!isEmpty(issuanceListQuery.data?.metrics)) {
      const statusMetrics = (
        issuanceListQuery.data as FetchServerPaginationIssuanceList.Return
      ).metrics.statusMetrics

      return statusMetrics
        .map((metric) => {
          const status = metricConfig.find(
            ({ id }) => id === metric.statusId.toString(),
          )

          if (status) {
            const statusLabel = ctIntl.formatMessage({ id: status.label })
            return {
              label: statusLabel,
              value: metric.count,
              ...(status.tab && {
                // oxlint-disable-next-line no-non-null-assertion
                onClick: () => handleCustomSelect(status.tab!, statusLabel),
              }),
            }
          }

          return null
        })
        .filter(Boolean) as Array<{
        label: string
        value: number
        onClick?: () => void
      }>
    }

    return []
  }, [issuanceListQuery.data, handleCustomSelect])

  const shouldShowCheckboxSelection =
    currentTab !== LIST_TABS.HISTORY &&
    ![
      CustomTabs.DECLINED,
      CustomTabs.CANCELLED,
      CustomTabs.RETURNED,
      CustomTabs.RETURNED_LATE,
    ].includes(customSelection as CustomTabs)

  const carpoolOptionsContextValue = useMemo(
    () => ({
      carpoolOptionsData:
        bookingOptionQuery.data ?? ({} as FetchCarpoolOptionsParsedData),
    }),
    [bookingOptionQuery.data],
  )

  return (
    <CarpoolOptionsContext.Provider value={carpoolOptionsContextValue}>
      <PageWithMainTableContainer>
        <Box sx={{ display: 'flex', justifyContent: 'space-between' }}>
          <Typography variant="h5">
            {ctIntl.formatMessage(
              { id: 'carpool.list.header' },
              { values: { carpoolAppName } },
            )}
          </Typography>
          <Button
            startIcon={<AddIcon />}
            size="small"
            onClick={() =>
              history.push(
                `${path}/${DETAILS_PREFIX}?${buildRouteQueryString({
                  schema: issuanceRequestSearchParamsSchema,
                  searchParams: { type: 'add' },
                })}`,
              )
            }
            color="primary"
            variant="contained"
          >
            {ctIntl.formatMessage(
              { id: 'carpool.newCarpoolRequest' },
              { values: { carpoolAppName } },
            )}
          </Button>
        </Box>
        {listMetrics.length > 0 && (
          <StatBar
            stats={listMetrics}
            isClickable={!customSelection}
            isLoading={issuanceListQuery.isPending}
          />
        )}

        <ContainerWithTabsForDataGrid
          renderTabs={() => (
            <ContainerWithTabsForDataGrid.Tabs
              value={currentTab}
              onChange={(_e, newValue) => setCurrentTab(newValue)}
            >
              {tabs.map(({ label, value, icon, iconPosition }) => (
                <ContainerWithTabsForDataGrid.Tab
                  key={value}
                  label={label}
                  value={value}
                  sx={{
                    height: '48px',
                    minHeight: '48px',
                  }}
                  {...(icon ? { icon, iconPosition } : {})}
                />
              ))}
            </ContainerWithTabsForDataGrid.Tabs>
          )}
        >
          <UserDataGridWithSavedSettingsOnIDB<IssuanceItem>
            Component={DataGridAsTabItem}
            dataGridId="currentIssuance"
            disableVirtualization
            loading={issuanceListQuery.isPending}
            rowCount={issuanceListQuery.data?.metrics?.total ?? 0}
            pagination
            filterDebounceMs={500} // since were doing server side-filtering, we can not afford a lower debounce (to not doo too many requests)
            pageSizeOptions={[10, 25, 50]}
            initialState={{
              pinnedColumns: {
                left: [GRID_CHECKBOX_SELECTION_COL_DEF.field, columnsIds.bookingNumber],
                right: ['actions'],
              },
            }}
            paginationMode="server"
            paginationModel={paginationModel}
            onPaginationModelChange={setPaginationModel}
            sortingMode="server"
            sortingOrder={['desc', 'asc']}
            sortModel={[sortModel]}
            onSortModelChange={onSortModelChange}
            filterMode="server"
            filterModel={filterModel}
            onFilterModelChange={onFilterModelChange}
            autosizeOnMount
            rows={issuanceList}
            columns={columns}
            disableRowSelectionOnClick
            checkboxSelection={shouldShowCheckboxSelection}
            onRowSelectionModelChange={handleSelectionModelChange}
            rowSelectionModel={multiSelectedBookingIds}
            slots={{
              toolbar: KarooToolbar,
              loadingOverlay: LinearProgress,
            }}
            slotProps={{
              toolbar: KarooToolbar.createProps({
                slots: {
                  searchFilter: { show: true, props: { quickSearchDebounceMs: 500 } },
                  settingsButton: { show: true },
                  filterButton: { show: true },
                },
                extraContent: {
                  right: (
                    <>
                      {shouldShowCheckboxSelection && (
                        <PopupState
                          variant="popover"
                          popupId="bulk-action"
                        >
                          {(popupState) => {
                            const builkActionOptions = getBulkActionOptions()
                            return (
                              <div>
                                <Button
                                  size="small"
                                  variant="outlined"
                                  color="secondary"
                                  sx={{
                                    width: '30px',
                                    height: '30px',
                                    minWidth: 'auto',
                                  }}
                                  disabled={isEmpty(builkActionOptions)}
                                  {...bindTrigger(popupState)}
                                >
                                  <ThreeDotsIcon />
                                </Button>
                                <Popover
                                  {...bindPopover(popupState)}
                                  anchorOrigin={{
                                    vertical: 'bottom',
                                    horizontal: 'center',
                                  }}
                                  transformOrigin={{
                                    vertical: 'top',
                                    horizontal: 'center',
                                  }}
                                >
                                  <MenuList>{builkActionOptions}</MenuList>
                                </Popover>
                              </div>
                            )
                          }}
                        </PopupState>
                      )}
                      <Button
                        sx={{ color: 'primary.dark' }}
                        variant="text"
                        startIcon={<InfoOutlinedIcon />}
                        size="small"
                        onClick={() => setCurrentDrawer({ type: 'status_info' })}
                      >
                        status info
                      </Button>
                    </>
                  ),
                },
              }),
              basePagination: {
                material: { showFirstButton: true, showLastButton: true },
              },
            }}
          />
        </ContainerWithTabsForDataGrid>

        {match(currentModal)
          .with(null, () => null)
          .with({ type: 'key_return' }, ({ data }) => (
            <KeyReturnModal
              onClose={() => setCurrentModal(null)}
              bookingId={data.bookingId}
              initialValues={data.initialValues}
            />
          ))
          .with({ type: 'cancel_booking' }, ({ data }) => (
            <CancelBookingModal
              onClose={() => setCurrentModal(null)}
              bookingIds={data.bookingIds}
            />
          ))
          .with({ type: 'force_terminate_booking' }, ({ data }) => (
            <ForceTerminateBookingModal
              onClose={() => setCurrentModal(null)}
              bookingIds={data.bookingIds}
            />
          ))
          .with({ type: 'end_booking' }, ({ data }) => (
            <EndBookingModal
              onClose={() => setCurrentModal(null)}
              bookingId={data.bookingId}
              bookingStatus={data.bookingStatus}
              initialValues={data.initialValues}
            />
          ))
          .with({ type: 'activate_booking' }, ({ data }) => (
            <ActivateBookingModal
              onClose={() => setCurrentModal(null)}
              bookingId={data.bookingId}
              initialValues={data.initialValues}
            />
          ))
          .exhaustive()}
        {match(currentDrawer)
          .with(null, () => null)
          .with({ type: 'activity_log' }, ({ data }) => (
            <ActivityLogDrawer
              onClose={() => setCurrentDrawer(null)}
              bookingId={data.bookingId}
              bookingStatus={data.bookingStatus}
              vehicleId={data.vehicleId}
              startDate={data.startDate}
              endDate={data.endDate}
            />
          ))
          .with({ type: 'status_info' }, () => (
            <StatusInfoDrawer onClose={() => setCurrentDrawer(null)} />
          ))
          .exhaustive()}
      </PageWithMainTableContainer>
    </CarpoolOptionsContext.Provider>
  )
}

export default ScdfList
